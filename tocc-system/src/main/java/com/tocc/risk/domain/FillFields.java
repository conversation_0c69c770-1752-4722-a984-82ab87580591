package com.tocc.risk.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 填报项字段对象 risk_fill_fields
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@ApiModel(value = "填报项字段对象")
@TableName("risk_fill_fields")
public class FillFields extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 字段名称 */
    @Excel(name = "字段名称")
    @ApiModelProperty(value = "字段名称")
    private String name;

    /** 填写方式（字典input_type） */
    @Excel(name = "填写方式", readConverterExp = "字=典input_type")
    @ApiModelProperty(value = "填写方式")
    private Integer type;

    /** 多选单选框数据逗号隔开 */
    @Excel(name = "多选单选框数据逗号隔开")
    @ApiModelProperty(value = "多选单选框数据逗号隔开")
    private String contents;

    /** 是否必填（1-是，0-否） */
    @Excel(name = "是否必填", readConverterExp = "1=-是，0-否")
    @ApiModelProperty(value = "是否必填")
    private Integer isMust;

    /** 逻辑说明 */
    @Excel(name = "逻辑说明")
    @ApiModelProperty(value = "逻辑说明")
    private String remarks;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "逻辑说明")
    private Integer delFlag;


//    public void setId(Long id)
//    {
//        this.id = id;
//    }
//
//    public Long getId()
//    {
//        return id;
//    }
//
//    public void setName(String name)
//    {
//        this.name = name;
//    }
//
//    public String getName()
//    {
//        return name;
//    }
//
//    public void setType(Integer type)
//    {
//        this.type = type;
//    }
//
//    public Integer getType()
//    {
//        return type;
//    }
//
//    public void setContents(String contents)
//    {
//        this.contents = contents;
//    }
//
//    public String getContents()
//    {
//        return contents;
//    }
//
//    public void setIsMust(Integer isMust)
//    {
//        this.isMust = isMust;
//    }
//
//    public Integer getIsMust()
//    {
//        return isMust;
//    }
//
//    public void setRemarks(String remarks)
//    {
//        this.remarks = remarks;
//    }
//
//    public String getRemarks()
//    {
//        return remarks;
//    }
//
//    public void setDelFlag(Integer delFlag)
//    {
//        this.delFlag = delFlag;
//    }
//
//    public Integer getDelFlag()
//    {
//        return delFlag;
//    }
//
//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("id", getId())
//            .append("name", getName())
//            .append("type", getType())
//            .append("contents", getContents())
//            .append("isMust", getIsMust())
//            .append("remarks", getRemarks())
//            .append("createTime", getCreateTime())
//            .append("updateTime", getUpdateTime())
//            .append("delFlag", getDelFlag())
//            .toString();
//    }
}
