package com.tocc.mapper;

import com.tocc.domain.dto.WarehouseMaterialDTO;
import com.tocc.domain.vo.MaterialVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库物资关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface WarehouseMaterialMapper {
    
    /**
     * 根据仓库ID查询物资列表
     *
     * @param warehouseId 仓库ID
     * @return 物资列表
     */
    List<MaterialVO> selectMaterialsByWarehouseId(String warehouseId);

    /**
     * 根据仓库ID和类型查询物资列表
     *
     * @param warehouseId 仓库ID
     * @param materialType 物资类型
     * @return 物资列表
     */
    List<MaterialVO> selectMaterialsByWarehouseIdAndType(@Param("warehouseId") String warehouseId, @Param("materialType") String materialType);

    /**
     * 批量插入仓库物资关联
     * 
     * @param warehouseMaterials 仓库物资关联列表
     * @return 结果
     */
    int batchInsertWarehouseMaterials(List<WarehouseMaterialDTO> warehouseMaterials);

    /**
     * 删除仓库的所有物资关联
     * 
     * @param warehouseId 仓库ID
     * @return 结果
     */
    int deleteWarehouseMaterialsByWarehouseId(String warehouseId);

    /**
     * 删除指定的仓库物资关联
     * 
     * @param warehouseId 仓库ID
     * @param materialId 物资ID
     * @return 结果
     */
    int deleteWarehouseMaterial(@Param("warehouseId") String warehouseId, @Param("materialId") String materialId);

    /**
     * 插入仓库物资关联
     * 
     * @param warehouseMaterial 仓库物资关联
     * @return 结果
     */
    int insertWarehouseMaterial(WarehouseMaterialDTO warehouseMaterial);
}
