package com.tocc.web.emerController;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.service.IWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仓库Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "仓库管理")
@RestController
@RequestMapping("/warehouse")
public class WarehouseController extends BaseController {
    
    @Autowired
    private IWarehouseService warehouseService;

    /**
     * 查询仓库列表
     */
    @ApiOperation("查询仓库列表")
    @PreAuthorize("@ss.hasPermi('warehouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarehouseDTO warehouse) {
        startPage();
        List<WarehouseVO> list = warehouseService.selectWarehouseList(warehouse);
        return getDataTable(list);
    }

    /**
     * 导出仓库列表
     */
    @ApiOperation("导出仓库列表")
    @PreAuthorize("@ss.hasPermi('warehouse:export')")
    @Log(title = "仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WarehouseDTO warehouse) {
        List<WarehouseVO> list = warehouseService.selectWarehouseList(warehouse);
        ExcelUtil<WarehouseVO> util = new ExcelUtil<WarehouseVO>(WarehouseVO.class);
        util.exportExcel(response, list, "仓库数据");
    }

    /**
     * 获取仓库详细信息
     */
    @ApiOperation("获取仓库详细信息")
    @PreAuthorize("@ss.hasPermi('warehouse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(warehouseService.selectWarehouseById(id));
    }

    /**
     * 新增仓库
     */
    @ApiOperation("新增仓库")
    @PreAuthorize("@ss.hasPermi('warehouse:add')")
    @Log(title = "仓库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WarehouseDTO warehouse) {
        return toAjax(warehouseService.insertWarehouse(warehouse));
    }

    /**
     * 修改仓库
     */
    @ApiOperation("修改仓库")
    @PreAuthorize("@ss.hasPermi('warehouse:edit')")
    @Log(title = "仓库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarehouseDTO warehouse) {
        return toAjax(warehouseService.updateWarehouse(warehouse));
    }

    /**
     * 删除仓库
     */
    @ApiOperation("删除仓库")
    @PreAuthorize("@ss.hasPermi('warehouse:remove')")
    @Log(title = "仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(warehouseService.deleteWarehouseByIds(ids));
    }

    /**
     * 查询仓库的物资列表
     */
    @ApiOperation("查询仓库的物资列表")
    @PreAuthorize("@ss.hasPermi('warehouse:query')")
    @GetMapping("/{warehouseId}/materials")
    public AjaxResult getWarehouseMaterials(@PathVariable String warehouseId) {
        List<MaterialVO> materials = warehouseService.selectWarehouseMaterials(warehouseId);
        return success(materials);
    }

    /**
     * 更新仓库的物资配置
     */
    @ApiOperation("更新仓库的物资配置")
    @PreAuthorize("@ss.hasPermi('warehouse:edit')")
    @Log(title = "仓库物资配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{warehouseId}/materials")
    public AjaxResult updateWarehouseMaterials(@PathVariable String warehouseId, @RequestBody List<String> materialIds) {
        return toAjax(warehouseService.updateWarehouseMaterials(warehouseId, materialIds));
    }

    /**
     * 添加仓库物资
     */
    @ApiOperation("添加仓库物资")
    @PreAuthorize("@ss.hasPermi('warehouse:edit')")
    @Log(title = "仓库物资", businessType = BusinessType.INSERT)
    @PostMapping("/{warehouseId}/material/{materialId}")
    public AjaxResult addWarehouseMaterial(@PathVariable String warehouseId, @PathVariable String materialId) {
        return toAjax(warehouseService.addWarehouseMaterial(warehouseId, materialId));
    }

    /**
     * 移除仓库物资
     */
    @ApiOperation("移除仓库物资")
    @PreAuthorize("@ss.hasPermi('warehouse:edit')")
    @Log(title = "仓库物资", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseId}/material/{materialId}")
    public AjaxResult removeWarehouseMaterial(@PathVariable String warehouseId, @PathVariable String materialId) {
        return toAjax(warehouseService.removeWarehouseMaterial(warehouseId, materialId));
    }
}
