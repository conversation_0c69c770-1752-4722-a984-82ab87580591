package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.em.dto.EmPrePlanDTO;
import com.tocc.em.qo.EmPrePlanQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急预案数据Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="应急预案数据")
@RestController
@RequestMapping("/em/prePlan")
public class EmPrePlanController extends BaseController
{
    @Autowired
    private IEmPrePlanService emPrePlanService;

    /**
     * 查询应急预案数据列表
     */
    @ApiOperation("查询应急预案数据列表")
    @PreAuthorize("@ss.hasPermi('em:prePlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanQO emPrePlanQO)
    {
        startPage();
        List<EmPrePlan> list = emPrePlanService.selectEmPrePlanList(emPrePlanQO);
        return getDataTable(list);
    }

    /**
     * 导出应急预案数据列表
     */
    @ApiOperation("导出应急预案数据列表")
    @PreAuthorize("@ss.hasPermi('em:prePlan:export')")
    @Log(title = "应急预案数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,EmPrePlanQO emPrePlanQO)
    {
        List<EmPrePlan> list = emPrePlanService.selectEmPrePlanList(emPrePlanQO);
        ExcelUtil<EmPrePlan> util = new ExcelUtil<EmPrePlan>(EmPrePlan.class);
        util.exportExcel(response, list, "应急预案数据数据");
    }

    /**
     * 获取应急预案数据详细信息
     */
    @ApiOperation("获取应急预案数据详细信息")
    @PreAuthorize("@ss.hasPermi('em:prePlan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanService.selectEmPrePlanById(id));
    }

    /**
     * 新增应急预案数据
     */
    @ApiOperation("新增应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:add')")
    @Log(title = "应急预案数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanDTO emPrePlanDTO)
    {
        return toAjax(emPrePlanService.insertEmPrePlan(emPrePlanDTO));
    }

    /**
     * 修改应急预案数据
     */
    @ApiOperation("修改应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:edit')")
    @Log(title = "应急预案数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanDTO emPrePlanDTO)
    {
        return toAjax(emPrePlanService.updateEmPrePlan(emPrePlanDTO));
    }

    /**
     * 删除应急预案数据
     */
    @ApiOperation("删除应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:remove')")
    @Log(title = "应急预案数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanService.deleteEmPrePlanByIds(ids));
    }
}
