<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.OrganizationTreeMapper">

    <resultMap type="com.tocc.system.domain.vo.OrganizationTreeVO" id="OrganizationTreeResult">
        <result property="id"         column="id"           />
        <result property="deptId"     column="dept_id"      />
        <result property="postId"     column="post_id"      />
        <result property="userId"     column="user_id"      />
        <result property="name"       column="name"         />
        <result property="type"       column="type"         />
        <result property="parentId"   column="parent_id"    />
        <result property="phone"      column="phone"        />
        <result property="email"      column="email"        />
        <result property="leader"     column="leader"       />
        <result property="nickName"   column="nick_name"    />
        <result property="userName"   column="user_name"    />
        <result property="postCode"   column="post_code"    />
        <result property="status"     column="status"       />
        <result property="orderNum"   column="order_num"    />
    </resultMap>

    <!-- 查询部门信息 -->
    <select id="selectDeptList" resultMap="OrganizationTreeResult">
        SELECT
            CONCAT('dept_', d.dept_id) as id,
            d.dept_id,
            NULL as post_id,
            NULL as user_id,
            d.dept_name as name,
            'dept' as type,
            CASE
                WHEN d.parent_id = 0 THEN NULL
                ELSE CONCAT('dept_', d.parent_id)
            END as parent_id,
            d.phone,
            d.email,
            d.leader,
            NULL as nick_name,
            NULL as user_name,
            NULL as post_code,
            d.status,
            d.order_num
        FROM sys_dept d
        WHERE d.del_flag = '0' AND d.status = '0'
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        ORDER BY d.parent_id, d.order_num
    </select>

    <!-- 根据部门ID查询岗位信息 -->
    <select id="selectPostsByDeptId" resultMap="OrganizationTreeResult">
        SELECT DISTINCT
            CONCAT('post_', p.post_id) as id,
            #{deptId} as dept_id,
            p.post_id,
            NULL as user_id,
            p.post_name as name,
            'post' as type,
            CONCAT('dept_', #{deptId}) as parent_id,
            NULL as phone,
            NULL as email,
            NULL as leader,
            NULL as nick_name,
            NULL as user_name,
            p.post_code,
            p.status,
            p.post_sort as order_num
        FROM sys_post p
        INNER JOIN sys_user_post up ON p.post_id = up.post_id
        INNER JOIN sys_user u ON up.user_id = u.user_id
        WHERE p.status = '0'
        AND u.del_flag = '0'
        AND u.dept_id = #{deptId}
        ORDER BY p.post_sort
    </select>

    <!-- 根据部门ID和岗位ID查询用户信息 -->
    <select id="selectUsersByDeptAndPost" resultMap="OrganizationTreeResult">
        SELECT
            CONCAT('user_', u.user_id) as id,
            u.dept_id,
            <choose>
                <when test="postId != null">
                    #{postId} as post_id,
                </when>
                <otherwise>
                    NULL as post_id,
                </otherwise>
            </choose>
            u.user_id,
            u.nick_name as name,
            'user' as type,
            <choose>
                <when test="postId != null">
                    CONCAT('post_', #{postId}) as parent_id,
                </when>
                <otherwise>
                    CONCAT('dept_', u.dept_id) as parent_id,
                </otherwise>
            </choose>
            u.phonenumber as phone,
            u.email,
            NULL as leader,
            u.nick_name,
            u.user_name,
            NULL as post_code,
            u.status,
            NULL as order_num
        FROM sys_user u
        WHERE u.del_flag = '0' AND u.status = '0'
        <if test="deptId != null">
            AND u.dept_id = #{deptId}
        </if>
        <if test="postId != null">
            AND EXISTS (
                SELECT 1 FROM sys_user_post up
                WHERE up.user_id = u.user_id
                AND up.post_id = #{postId}
            )
        </if>
        ORDER BY u.nick_name
    </select>

    <!-- 查询完整的组织架构树形数据 -->
    <select id="selectOrganizationTree" resultMap="OrganizationTreeResult">
        <!-- 部门数据 -->
        SELECT
            CONCAT('dept_', d.dept_id) as id,
            d.dept_id,
            NULL as post_id,
            NULL as user_id,
            d.dept_name as name,
            'dept' as type,
            CASE
                WHEN d.parent_id = 0 THEN NULL
                ELSE CONCAT('dept_', d.parent_id)
            END as parent_id,
            d.phone,
            d.email,
            d.leader,
            NULL as nick_name,
            NULL as user_name,
            NULL as post_code,
            d.status,
            d.order_num
        FROM sys_dept d
        WHERE d.del_flag = '0' AND d.status = '0'
        <if test="deptId != null">
            AND (d.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        UNION ALL

        <!-- 岗位数据 -->
        SELECT DISTINCT
            CONCAT('post_', p.post_id) as id,
            u.dept_id,
            p.post_id,
            NULL as user_id,
            p.post_name as name,
            'post' as type,
            CONCAT('dept_', u.dept_id) as parent_id,
            NULL as phone,
            NULL as email,
            NULL as leader,
            NULL as nick_name,
            NULL as user_name,
            p.post_code,
            p.status,
            p.post_sort as order_num
        FROM sys_post p
        INNER JOIN sys_user_post up ON p.post_id = up.post_id
        INNER JOIN sys_user u ON up.user_id = u.user_id
        INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE p.status = '0'
        AND u.del_flag = '0'
        AND u.status = '0'
        AND d.del_flag = '0'
        AND d.status = '0'
        <if test="deptId != null">
            AND (u.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        UNION ALL

        <!-- 用户数据 -->
        SELECT
            CONCAT('user_', u.user_id) as id,
            u.dept_id,
            up.post_id,
            u.user_id,
            u.nick_name as name,
            'user' as type,
            CASE
                WHEN up.post_id IS NOT NULL THEN CONCAT('post_', up.post_id)
                ELSE CONCAT('dept_', u.dept_id)
            END as parent_id,
            u.phonenumber as phone,
            u.email,
            NULL as leader,
            u.nick_name,
            u.user_name,
            NULL as post_code,
            u.status,
            NULL as order_num
        FROM sys_user u
        LEFT JOIN sys_user_post up ON u.user_id = up.user_id
        INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.del_flag = '0'
        AND u.status = '0'
        AND d.del_flag = '0'
        AND d.status = '0'
        <if test="deptId != null">
            AND (u.dept_id = #{deptId} OR FIND_IN_SET(CAST(#{deptId} AS CHAR), d.ancestors))
        </if>

        ORDER BY type, order_num, name
    </select>

</mapper>
