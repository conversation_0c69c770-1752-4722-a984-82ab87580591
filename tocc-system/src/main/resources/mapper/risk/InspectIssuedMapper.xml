<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.InspectIssuedMapper">
    
    <resultMap type="InspectIssued" id="InspectIssuedResult">
        <result property="id"    column="id"    />
        <result property="projectsId"    column="projects_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="units"    column="units"    />
        <result property="unitId"    column="unit_id"    />
        <result property="fields"    column="fields"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="remarks"    column="remarks"    />
        <result property="issuedUnit"    column="issued_unit"    />
        <result property="issuedUnitId"    column="issued_unit_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectInspectIssuedVo">
        select id, projects_id, name, type, units, unit_id, fields, end_time, status, remarks, issued_unit, issued_unit_id, create_by, create_by_id, update_time, del_flag from risk_inspect_issued
    </sql>

    <select id="selectInspectIssuedList" parameterType="InspectIssued" resultMap="InspectIssuedResult">
        <include refid="selectInspectIssuedVo"/>
        <where>  
            <if test="projectsId != null "> and projects_id = #{projectsId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="unitId != null  and unitId != ''"> and unit_id = #{unitId}</if>
            <if test="fields != null  and fields != ''"> and fields = #{fields}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="issuedUnit != null  and issuedUnit != ''"> and issued_unit = #{issuedUnit}</if>
            <if test="issuedUnitId != null "> and issued_unit_id = #{issuedUnitId}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>
    
    <select id="selectInspectIssuedById" parameterType="Long" resultMap="InspectIssuedResult">
        <include refid="selectInspectIssuedVo"/>
        where id = #{id}
    </select>

    <insert id="insertInspectIssued" parameterType="InspectIssued" keyProperty="id">
        insert into risk_inspect_issued
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectsId != null">projects_id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="units != null">units,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="fields != null">fields,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="issuedUnit != null">issued_unit,</if>
            <if test="issuedUnitId != null">issued_unit_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectsId != null">#{projectsId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="units != null">#{units},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="fields != null">#{fields},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="issuedUnit != null">#{issuedUnit},</if>
            <if test="issuedUnitId != null">#{issuedUnitId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateInspectIssued" parameterType="InspectIssued">
        update risk_inspect_issued
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectsId != null">projects_id = #{projectsId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="units != null">units = #{units},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="fields != null">fields = #{fields},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="issuedUnit != null">issued_unit = #{issuedUnit},</if>
            <if test="issuedUnitId != null">issued_unit_id = #{issuedUnitId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInspectIssuedById" parameterType="Long">
        delete from risk_inspect_issued where id = #{id}
    </delete>

    <delete id="deleteInspectIssuedByIds" parameterType="String">
        delete from risk_inspect_issued where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>