package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.Projects;
import com.tocc.risk.service.IRiskProjectsService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 项目Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags = "在建项目")
@RestController
@RequestMapping("/risk/projects")
public class ProjectsController extends BaseController
{
    @Autowired
    private IRiskProjectsService riskProjectsService;

    /**
     * 查询项目列表
     */
    @ApiOperation("获取项目列表")
//    @PreAuthorize("@ss.hasPermi('system:projects:list')")
    @GetMapping("/list")
    public TableDataInfo list(Projects projects)
    {
        startPage();
        List<Projects> list = riskProjectsService.selectRiskProjectsList(projects);
        return getDataTable(list);
    }

    /**
     * 导出项目列表
     */
    @ApiOperation("导出项目列表")
//    @PreAuthorize("@ss.hasPermi('system:projects:export')")
    @Log(title = "项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Projects projects)
    {
        List<Projects> list = riskProjectsService.selectRiskProjectsList(projects);
        ExcelUtil<Projects> util = new ExcelUtil<Projects>(Projects.class);
        util.exportExcel(response, list, "项目数据");
    }

    /**
     * 获取项目详细信息
     */
    @ApiOperation("获取项目详情")
//    @PreAuthorize("@ss.hasPermi('system:projects:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(riskProjectsService.selectRiskProjectsById(id));
    }

    /**
     * 新增项目
     */
    @ApiOperation("新增项目")
//    @PreAuthorize("@ss.hasPermi('system:projects:add')")
    @Log(title = "项目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Projects projects)
    {
        return toAjax(riskProjectsService.insertRiskProjects(projects));
    }

    /**
     * 修改项目
     */
    @ApiOperation("更新项目")
//    @PreAuthorize("@ss.hasPermi('system:projects:edit')")
    @Log(title = "项目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody Projects projects)
    {
        return toAjax(riskProjectsService.updateRiskProjects(projects));
    }

    /**
     * 删除项目
     */
    @ApiOperation("删除项目")
//    @PreAuthorize("@ss.hasPermi('system:projects:remove')")
    @Log(title = "项目", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody Projects projects)
    {
        return toAjax(riskProjectsService.deleteRiskProjectsById(projects.getId()));
    }
}
