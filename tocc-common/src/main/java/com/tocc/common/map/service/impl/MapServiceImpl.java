package com.tocc.common.map.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.map.domain.MapVO;
import com.tocc.common.map.service.IMapService;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;

@Service("mapService")
public class MapServiceImpl implements IMapService {

    private static String KEY= "50b02607ff4943ab7d3ac53c49fee7d6";

    public  static String GD_URL= "https://restapi.amap.com/v3/geocode/geo?address=%s&key=%s";

    // 地址转经纬度
    public MapVO getLonAndLatByAddress(String address) {
        MapVO mapVO = new MapVO();
        String requestUrl = String.format(GD_URL, address, KEY);
        //高德接口返回的是JSON格式的字符串
        String queryResult = getResponse(requestUrl);
        JSONObject obj = JSONObject.parseObject(queryResult);
        if(String.valueOf(obj.get("status")).equals("1")){
            JSONObject jobJSON = JSONObject.parseObject(obj.get("geocodes").toString().substring(1, obj.get("geocodes").toString().length() - 1));
            String location = String.valueOf(jobJSON.get("location"));
            // localtion中逗号前面为经度，后面为纬度
            String[] parts = location.split(",");
            mapVO.setLng(parts[0].trim());
            mapVO.setLat(parts[1].trim());
        }else{
            throw new RuntimeException("地址转换经纬度失败，错误码：" + obj.get("infocode"));
        }

        return mapVO;

    }

    private static String getResponse(String serverUrl) {
        // 用JAVA发起http请求，并返回json格式的结果
        StringBuffer result = new StringBuffer();
        try {
            URL url = new URL(serverUrl);
            URLConnection conn = url.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

}
