package com.tocc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * 仓库VO
 * 
 * <AUTHOR>
 */
public class WarehouseVO {
    
    /** 主键ID */
    private String id;

    /** 仓库名称 */
    private String warehouseName;

    /** 仓库类型 */
    private String warehouseType;

    /** 仓库类型名称 */
    private String warehouseTypeName;

    /** 所属单位code */
    private String belongOrgCode;

    /** 所属单位名称 */
    private String belongOrgName;

    /** 详细地址 */
    private String address;

    /** 负责人姓名 */
    private String principal;

    /** 联系电话 */
    private String contactPhone;

    /** 路段编号 */
    private String roadCode;

    /** 桩号 */
    private String stake;

    /** 纬度 */
    private String latitude;

    /** 经度 */
    private String longitude;

    /** 备注信息 */
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建人 */
    private String creator;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新人 */
    private String updater;

    /** 物资列表（应急物资） */
    private List<MaterialVO> materials;

    /** 装备列表（应急装备） */
    private List<MaterialVO> equipments;

    /** 物资总数量 */
    private Integer totalMaterialCount;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType;
    }

    public String getWarehouseTypeName() {
        return warehouseTypeName;
    }

    public void setWarehouseTypeName(String warehouseTypeName) {
        this.warehouseTypeName = warehouseTypeName;
    }

    public String getBelongOrgCode() {
        return belongOrgCode;
    }

    public void setBelongOrgCode(String belongOrgCode) {
        this.belongOrgCode = belongOrgCode;
    }

    public String getBelongOrgName() {
        return belongOrgName;
    }

    public void setBelongOrgName(String belongOrgName) {
        this.belongOrgName = belongOrgName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getRoadCode() {
        return roadCode;
    }

    public void setRoadCode(String roadCode) {
        this.roadCode = roadCode;
    }

    public String getStake() {
        return stake;
    }

    public void setStake(String stake) {
        this.stake = stake;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public List<MaterialVO> getMaterials() {
        return materials;
    }

    public void setMaterials(List<MaterialVO> materials) {
        this.materials = materials;
    }

    public List<MaterialVO> getEquipments() {
        return equipments;
    }

    public void setEquipments(List<MaterialVO> equipments) {
        this.equipments = equipments;
    }

    public Integer getTotalMaterialCount() {
        return totalMaterialCount;
    }

    public void setTotalMaterialCount(Integer totalMaterialCount) {
        this.totalMaterialCount = totalMaterialCount;
    }

    @Override
    public String toString() {
        return "WarehouseVO{" +
                "id='" + id + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", warehouseType='" + warehouseType + '\'' +
                ", warehouseTypeName='" + warehouseTypeName + '\'' +
                ", belongOrgCode='" + belongOrgCode + '\'' +
                ", belongOrgName='" + belongOrgName + '\'' +
                ", address='" + address + '\'' +
                ", principal='" + principal + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", roadCode='" + roadCode + '\'' +
                ", stake='" + stake + '\'' +
                ", latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", creator='" + creator + '\'' +
                ", updateTime=" + updateTime +
                ", updater='" + updater + '\'' +
                ", materials=" + materials +
                ", equipments=" + equipments +
                ", totalMaterialCount=" + totalMaterialCount +
                '}';
    }
}
