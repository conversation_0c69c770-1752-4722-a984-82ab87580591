package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.Approve;
import com.tocc.risk.service.IApproveService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 检查审批Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@RestController
@RequestMapping("/risk/approve")
public class ApproveController extends BaseController
{
    @Autowired
    private IApproveService approveService;

    /**
     * 查询检查审批列表
     */
    @PreAuthorize("@ss.hasPermi('system:approve:list')")
    @GetMapping("/list")
    public TableDataInfo list(Approve approve)
    {
        startPage();
        List<Approve> list = approveService.selectApproveList(approve);
        return getDataTable(list);
    }

    /**
     * 导出检查审批列表
     */
    @PreAuthorize("@ss.hasPermi('system:approve:export')")
    @Log(title = "检查审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Approve approve)
    {
        List<Approve> list = approveService.selectApproveList(approve);
        ExcelUtil<Approve> util = new ExcelUtil<Approve>(Approve.class);
        util.exportExcel(response, list, "检查审批数据");
    }

    /**
     * 获取检查审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:approve:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(approveService.selectApproveById(id));
    }

    /**
     * 新增检查审批
     */
    @PreAuthorize("@ss.hasPermi('system:approve:add')")
    @Log(title = "检查审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Approve approve)
    {
        return toAjax(approveService.insertApprove(approve));
    }

    /**
     * 修改检查审批
     */
    @PreAuthorize("@ss.hasPermi('system:approve:edit')")
    @Log(title = "检查审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Approve approve)
    {
        return toAjax(approveService.updateApprove(approve));
    }

    /**
     * 删除检查审批
     */
    @PreAuthorize("@ss.hasPermi('system:approve:remove')")
    @Log(title = "检查审批", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody Approve approve)
    {
        return toAjax(approveService.deleteApproveById(approve.getId()));
    }
}
