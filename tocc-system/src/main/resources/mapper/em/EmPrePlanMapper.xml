<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmPrePlanMapper">

    <resultMap type="EmPrePlan" id="EmPrePlanResult">
        <result property="id"    column="id"    />
        <result property="compilingDept"    column="compiling_dept"    />
        <result property="version"    column="version"    />
        <result property="planName"    column="plan_name"    />
        <result property="planType"    column="plan_type"    />
        <result property="applicableDeptIds"    column="applicable_dept_ids"    />
        <result property="purpose"    column="purpose"    />
        <result property="basis"    column="basis"    />
        <result property="scope"    column="scope"    />
        <result property="workPrinciple"    column="work_principle"    />
        <result property="preventiveMeasures"    column="preventive_measures"    />
        <result property="warningPrinciple"    column="warning_principle"    />
        <result property="warningInfoCollect"    column="warning_info_collect"    />
        <result property="warningLevel"    column="warning_level"    />
        <result property="warningPublish"    column="warning_publish"    />
        <result property="warningMeasures"    column="warning_measures"    />
        <result property="eventLevel"    column="event_level"    />
        <result property="responseCondition"    column="response_condition"    />
        <result property="processFlow"    column="process_flow"    />
        <result property="infoReport"    column="info_report"    />
        <result property="newsRelease"    column="news_release"    />
        <result property="responseAdjust"    column="response_adjust"    />
        <result property="aftermathDisposal"    column="aftermath_disposal"    />
        <result property="summaryEvaluation"    column="summary_evaluation"    />
        <result property="materialSupport"    column="material_support"    />
        <result property="communicationSupport"    column="communication_support"    />
        <result property="trafficSupport"    column="traffic_support"    />
        <result property="fundingSupport"    column="funding_support"    />
        <result property="planRevision"    column="plan_revision"    />
        <result property="publicityTraining"    column="publicity_training"    />
        <result property="planDrill"    column="plan_drill"    />
        <result property="implementTime"    column="implement_time"    />
        <result property="planStatus"    column="plan_status"    />
        <result property="enableStatus"    column="enable_status"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="lastCheckTime"    column="last_check_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="reviser"    column="reviser"    />
        <result property="revisionTime"    column="revision_time"    />
        <result property="revisionContent"    column="revision_content"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmPrePlanVo">
           select
            p.id,
            p.compiling_dept,
            p.version,
            p.plan_name,
            p.plan_type,
            p.applicable_dept_ids,
            p.purpose,
            p.basis,
            p.scope,
            p.work_principle,
            p.preventive_measures,
            p.warning_principle,
            p.warning_info_collect,
            p.warning_level,
            p.warning_publish,
            p.warning_measures,
            p.event_level,
            p.response_condition,
            p.process_flow,
            p.info_report,
            p.news_release,
            p.response_adjust,
            p.aftermath_disposal,
            p.summary_evaluation,
            p.material_support,
            p.communication_support,
            p.traffic_support,
            p.funding_support,
            p.plan_revision,
            p.publicity_training,
            p.plan_drill,
            p.implement_time,
            p.plan_status,
            p.enable_status,
            p.check_status,
            p.last_check_time,
            p.create_time,
            p.creator,
            p.update_time,
            p.updater,
            p.reviser,
            p.revision_time,
            p.revision_content,
            p.del_flag
        from em_pre_plan  p
    </sql>

    <select id="selectEmPrePlanList" parameterType="com.tocc.em.qo.EmPrePlanQO" resultMap="EmPrePlanResult">
        <include refid="selectEmPrePlanVo"/>
           left join sys_dept d on d.dept_id = compiling_dept
        <where>
            p.del_flag = 0 and d.del_flag = 0
            <if test="compilingDept != null  and compilingDept != ''"> and p.compiling_dept = #{compilingDept}</if>
            <if test="version != null  and version != ''"> and p.version = #{version}</if>
            <if test="planName != null  and planName != ''"> and p.plan_name like concat('%', #{planName}, '%')</if>
            <if test="planType != null  and planType != ''"> and p.plan_type = #{planType}</if>
            <if test="applicableDeptIds != null  and applicableDeptIds != ''">
             and p.applicable_dept_ids like concat('%' #{applicableDeptIds} , '%')
             </if>
            <if test="purpose != null  and purpose != ''"> and p.purpose = #{purpose}</if>
            <if test="basis != null  and basis != ''"> and p.basis = #{basis}</if>
            <if test="scope != null  and scope != ''"> and p.scope = #{scope}</if>
            <if test="workPrinciple != null  and workPrinciple != ''"> and p.work_principle = #{workPrinciple}</if>
            <if test="preventiveMeasures != null  and preventiveMeasures != ''"> and p.preventive_measures = #{preventiveMeasures}</if>
            <if test="warningPrinciple != null  and warningPrinciple != ''"> and p.warning_principle = #{warningPrinciple}</if>
            <if test="warningInfoCollect != null  and warningInfoCollect != ''"> and p.warning_info_collect = #{warningInfoCollect}</if>
            <if test="warningLevel != null  and warningLevel != ''"> and p.warning_level = #{warningLevel}</if>
            <if test="warningPublish != null  and warningPublish != ''"> and p.warning_publish = #{warningPublish}</if>
            <if test="warningMeasures != null  and warningMeasures != ''"> and p.warning_measures = #{warningMeasures}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and p.event_level = #{eventLevel}</if>
            <if test="responseCondition != null  and responseCondition != ''"> and p.response_condition = #{responseCondition}</if>
            <if test="processFlow != null  and processFlow != ''"> and p.process_flow = #{processFlow}</if>
            <if test="infoReport != null  and infoReport != ''"> and p.info_report = #{infoReport}</if>
            <if test="newsRelease != null  and newsRelease != ''"> and p.news_release = #{newsRelease}</if>
            <if test="responseAdjust != null  and responseAdjust != ''"> and p.response_adjust = #{responseAdjust}</if>
            <if test="aftermathDisposal != null  and aftermathDisposal != ''"> and p.aftermath_disposal = #{aftermathDisposal}</if>
            <if test="summaryEvaluation != null  and summaryEvaluation != ''"> and p.summary_evaluation = #{summaryEvaluation}</if>
            <if test="materialSupport != null  and materialSupport != ''"> and p.material_support = #{materialSupport}</if>
            <if test="communicationSupport != null  and communicationSupport != ''"> and p.communication_support = #{communicationSupport}</if>
            <if test="trafficSupport != null  and trafficSupport != ''"> and p.traffic_support = #{trafficSupport}</if>
            <if test="fundingSupport != null  and fundingSupport != ''"> and p.funding_support = #{fundingSupport}</if>
            <if test="planRevision != null  and planRevision != ''"> and p.plan_revision = #{planRevision}</if>
            <if test="publicityTraining != null  and publicityTraining != ''"> and p.publicity_training = #{publicityTraining}</if>
            <if test="planDrill != null  and planDrill != ''"> and p.plan_drill = #{planDrill}</if>
            <if test="implementTime != null  and implementTime != ''"> and p.implement_time = #{implementTime}</if>
            <if test="planStatus != null "> and p.plan_status = #{planStatus}</if>
            <if test="enableStatus != null ">
                    and p.enable_status = #{enableStatus}
                <if test="enableStatus == 0 ">
                    and p.update_time BETWEEN  #{param.weekStart} and #{param.weekEnd}
                </if>
                <if test="enableStatus == 1 ">
                    and p.last_check_time BETWEEN  #{param.weekStart} and #{param.weekEnd}
                </if>
            </if>
            <if test="checkStatus != null "> and p.check_status = #{checkStatus}</if>
            <if test="lastCheckTime != null "> and p.last_check_time = #{lastCheckTime}</if>
            <if test="creator != null  and creator != ''"> and p.creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and p.updater = #{updater}</if>
            <if test="reviser != null  and reviser != ''"> and p.reviser = #{reviser}</if>
            <if test="revisionTime != null "> and p.revision_time = #{revisionTime}</if>
            <if test="revisionContent != null  and revisionContent != ''"> and p.revision_content = #{revisionContent}</if>
            <if test="revisionTime != null "> and p.revision_time = #{revisionTime}</if>
            <if test="searchValue != null ">
             and CONCAT_WS('',
                p.version,p.plan_name, p.plan_type, p.purpose,p. basis,p. scope,
                p.work_principle, p.preventive_measures, p.warning_principle,
                p.warning_info_collect,p. warning_level, p.warning_publish,
                p.warning_measures, p.event_level, p.response_condition,
                p.process_flow, p.info_report, p.news_release, p.response_adjust,
                p.aftermath_disposal, p.summary_evaluation, p.material_support,
                p.communication_support, p.traffic_support, p.funding_support,
                p.plan_revision, p.publicity_training,p. plan_drill,
                p.implement_time, p.plan_status, p.reviser, p.revision_content
                ) LIKE CONCAT('%',#{searchValue},'%')
             </if>
            <if test="deptType != null  and deptType != ''"> and d.dept_type = #{deptType}</if>

        </where>
        order by p.create_time desc
    </select>

    <select id="selectEmPrePlanById" parameterType="String" resultMap="EmPrePlanResult">
        <include refid="selectEmPrePlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmPrePlan" parameterType="EmPrePlan">
        insert into em_pre_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="compilingDept != null">compiling_dept,</if>
            <if test="version != null">version,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planType != null">plan_type,</if>
            <if test="applicableDeptIds != null">applicable_dept_ids,</if>
            <if test="purpose != null">purpose,</if>
            <if test="basis != null">basis,</if>
            <if test="scope != null">scope,</if>
            <if test="workPrinciple != null">work_principle,</if>
            <if test="preventiveMeasures != null">preventive_measures,</if>
            <if test="warningPrinciple != null">warning_principle,</if>
            <if test="warningInfoCollect != null">warning_info_collect,</if>
            <if test="warningLevel != null">warning_level,</if>
            <if test="warningPublish != null">warning_publish,</if>
            <if test="warningMeasures != null">warning_measures,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="responseCondition != null">response_condition,</if>
            <if test="processFlow != null">process_flow,</if>
            <if test="infoReport != null">info_report,</if>
            <if test="newsRelease != null">news_release,</if>
            <if test="responseAdjust != null">response_adjust,</if>
            <if test="aftermathDisposal != null">aftermath_disposal,</if>
            <if test="summaryEvaluation != null">summary_evaluation,</if>
            <if test="materialSupport != null">material_support,</if>
            <if test="communicationSupport != null">communication_support,</if>
            <if test="trafficSupport != null">traffic_support,</if>
            <if test="fundingSupport != null">funding_support,</if>
            <if test="planRevision != null">plan_revision,</if>
            <if test="publicityTraining != null">publicity_training,</if>
            <if test="planDrill != null">plan_drill,</if>
            <if test="implementTime != null">implement_time,</if>
            <if test="planStatus != null">plan_status,</if>
            <if test="enableStatus != null">enable_status,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="lastCheckTime != null">last_check_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="reviser != null">reviser,</if>
            <if test="revisionTime != null">revision_time,</if>
            <if test="revisionContent != null">revision_content,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="compilingDept != null">#{compilingDept},</if>
            <if test="version != null">#{version},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planType != null">#{planType},</if>
            <if test="applicableDeptIds != null">#{applicableDeptIds},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="basis != null">#{basis},</if>
            <if test="scope != null">#{scope},</if>
            <if test="workPrinciple != null">#{workPrinciple},</if>
            <if test="preventiveMeasures != null">#{preventiveMeasures},</if>
            <if test="warningPrinciple != null">#{warningPrinciple},</if>
            <if test="warningInfoCollect != null">#{warningInfoCollect},</if>
            <if test="warningLevel != null">#{warningLevel},</if>
            <if test="warningPublish != null">#{warningPublish},</if>
            <if test="warningMeasures != null">#{warningMeasures},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="responseCondition != null">#{responseCondition},</if>
            <if test="processFlow != null">#{processFlow},</if>
            <if test="infoReport != null">#{infoReport},</if>
            <if test="newsRelease != null">#{newsRelease},</if>
            <if test="responseAdjust != null">#{responseAdjust},</if>
            <if test="aftermathDisposal != null">#{aftermathDisposal},</if>
            <if test="summaryEvaluation != null">#{summaryEvaluation},</if>
            <if test="materialSupport != null">#{materialSupport},</if>
            <if test="communicationSupport != null">#{communicationSupport},</if>
            <if test="trafficSupport != null">#{trafficSupport},</if>
            <if test="fundingSupport != null">#{fundingSupport},</if>
            <if test="planRevision != null">#{planRevision},</if>
            <if test="publicityTraining != null">#{publicityTraining},</if>
            <if test="planDrill != null">#{planDrill},</if>
            <if test="implementTime != null">#{implementTime},</if>
            <if test="planStatus != null">#{planStatus},</if>
            <if test="enableStatus != null">#{enableStatus},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="lastCheckTime != null">#{lastCheckTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="reviser != null">#{reviser},</if>
            <if test="revisionTime != null">#{revisionTime},</if>
            <if test="revisionContent != null">#{revisionContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmPrePlan" parameterType="EmPrePlan">
        update em_pre_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="compilingDept != null">compiling_dept = #{compilingDept},</if>
            <if test="version != null">version = #{version},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="applicableDeptIds != null">applicable_dept_ids = #{applicableDeptIds},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="basis != null">basis = #{basis},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="workPrinciple != null">work_principle = #{workPrinciple},</if>
            <if test="preventiveMeasures != null">preventive_measures = #{preventiveMeasures},</if>
            <if test="warningPrinciple != null">warning_principle = #{warningPrinciple},</if>
            <if test="warningInfoCollect != null">warning_info_collect = #{warningInfoCollect},</if>
            <if test="warningLevel != null">warning_level = #{warningLevel},</if>
            <if test="warningPublish != null">warning_publish = #{warningPublish},</if>
            <if test="warningMeasures != null">warning_measures = #{warningMeasures},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="responseCondition != null">response_condition = #{responseCondition},</if>
            <if test="processFlow != null">process_flow = #{processFlow},</if>
            <if test="infoReport != null">info_report = #{infoReport},</if>
            <if test="newsRelease != null">news_release = #{newsRelease},</if>
            <if test="responseAdjust != null">response_adjust = #{responseAdjust},</if>
            <if test="aftermathDisposal != null">aftermath_disposal = #{aftermathDisposal},</if>
            <if test="summaryEvaluation != null">summary_evaluation = #{summaryEvaluation},</if>
            <if test="materialSupport != null">material_support = #{materialSupport},</if>
            <if test="communicationSupport != null">communication_support = #{communicationSupport},</if>
            <if test="trafficSupport != null">traffic_support = #{trafficSupport},</if>
            <if test="fundingSupport != null">funding_support = #{fundingSupport},</if>
            <if test="planRevision != null">plan_revision = #{planRevision},</if>
            <if test="publicityTraining != null">publicity_training = #{publicityTraining},</if>
            <if test="planDrill != null">plan_drill = #{planDrill},</if>
            <if test="implementTime != null">implement_time = #{implementTime},</if>
            <if test="planStatus != null">plan_status = #{planStatus},</if>
            <if test="enableStatus != null">enable_status = #{enableStatus},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="lastCheckTime != null">last_check_time = #{lastCheckTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="reviser != null">reviser = #{reviser},</if>
            <if test="revisionTime != null">revision_time = #{revisionTime},</if>
            <if test="revisionContent != null">revision_content = #{revisionContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmPrePlanById" parameterType="String">
        update  em_pre_plan set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmPrePlanByIds" parameterType="String">
        delete from em_pre_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTimeoutPlans" resultMap="EmPrePlanResult">
        <include refid="selectEmPrePlanVo"/>
        where del_flag = 0
          and enable_status = 0
          and update_time &lt; #{timeoutTime}
        order by update_time asc
    </select>
</mapper>
