<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.InspectTaskMapper">
    
    <resultMap type="InspectTask" id="InspectTaskResult">
        <result property="id"    column="id"    />
        <result property="issuedId"    column="issued_id"    />
        <result property="informantId"    column="Informant_id"    />
        <result property="Informant"    column="Informant"    />
        <result property="contents"    column="contents"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectInspectTaskVo">
        select id, issued_id, Informant_id, Informant, contents, status, create_time, update_time, del_flag from risk_inspect_task
    </sql>

    <select id="selectInspectTaskList" parameterType="InspectIssued">
        select it.id, ii.name, ii.type, ii.issued_unit, ii.end_time, it.status
        from risk_inspect_task it
        left join risk_inspect_issued ii on it.issued_id = ii.id
        where it.Informant_id = #{informantId}
    </select>
    
    <select id="selectInspectTaskById" parameterType="Long" resultMap="InspectTaskResult">
        <include refid="selectInspectTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertInspectTask" parameterType="InspectTask">
        insert into risk_inspect_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="issuedId != null">issued_id,</if>
            <if test="informantId != null">Informant_id,</if>
            <if test="Informant != null">Informant,</if>
            <if test="contents != null">contents,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="issuedId != null">#{issuedId},</if>
            <if test="informantId != null">#{informantId},</if>
            <if test="Informant != null">#{Informant},</if>
            <if test="contents != null">#{contents},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateInspectTask" parameterType="InspectTask">
        update risk_inspect_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="issuedId != null">issued_id = #{issuedId},</if>
            <if test="informantId != null">Informant_id = #{informantId},</if>
            <if test="Informant != null">Informant = #{Informant},</if>
            <if test="contents != null">contents = #{contents},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInspectTaskById" parameterType="Long">
        delete from risk_inspect_task where id = #{id}
    </delete>

    <delete id="deleteInspectTaskByIds" parameterType="String">
        delete from risk_inspect_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>