# 仓库管理功能API接口文档

## 功能概述

仓库管理功能提供了完整的应急物资仓库信息管理，包括仓库基础信息、物资配置等功能。新增仓库时可以同时创建仓库专属的物资。

## 数据库表结构

### 1. 仓库主表 (em_warehouse)
- 仓库基础信息：名称、类型、所属单位、地址、负责人等
- 地理位置信息：经纬度、路段编号、桩号

### 2. 物资关联表 (em_warehouse_material)
- 简单的多对多关联关系
- 联合主键：(warehouse_id, material_id)

### 3. 物资表 (em_material)
- 物资基础信息，通过关联表与仓库建立关系
- warehouse_id字段用于标识仓库专属物资

## API接口列表

### 1. 基础CRUD接口

#### 1.1 查询仓库列表
```
GET /warehouse/list
```

**参数：**
- `pageNum`: 页码
- `pageSize`: 每页大小
- `warehouseName`: 仓库名称（模糊查询）
- `warehouseType`: 仓库类型
- `belongOrgCode`: 所属单位编码
- `belongOrgName`: 所属单位名称（模糊查询）
- `address`: 详细地址（模糊查询）
- `principal`: 负责人姓名（模糊查询）
- `contactPhone`: 联系电话
- `roadCode`: 路段编号
- `params[beginTime]`: 开始时间
- `params[endTime]`: 结束时间

#### 1.2 获取仓库详情
```
GET /warehouse/{id}
```

#### 1.3 新增仓库（同时创建专属物资）
```
POST /warehouse
Content-Type: application/json

{
  "warehouseName": "中心应急仓库",
  "warehouseType": "1",
  "belongOrgCode": "ORG001",
  "belongOrgName": "市应急管理局",
  "address": "南宁市青秀区应急路100号",
  "principal": "王管理",
  "contactPhone": "13800138000",
  "roadCode": "G72",
  "stake": "K100+500",
  "latitude": "22.817002",
  "longitude": "108.366543",
  "remark": "中心应急物资仓库",
  "materials": [
    {
      "materialName": "应急帐篷",
      "materialType": "0",
      "specModel": "3x3米",
      "categoryCode": "SHELTER",
      "quantity": 100,
      "unit": "顶",
      "expiryDate": "2025-12-31",
      "remark": "防水防风应急帐篷"
    },
    {
      "materialName": "发电机",
      "materialType": "1",
      "specModel": "5KW汽油发电机",
      "categoryCode": "POWER",
      "quantity": 10,
      "unit": "台",
      "remark": "应急供电设备"
    }
  ]
}
```

#### 1.4 修改仓库
```
PUT /warehouse
Content-Type: application/json
```

#### 1.5 删除仓库
```
DELETE /warehouse/{ids}
```

### 2. 物资管理接口

#### 2.1 查询仓库物资列表
```
GET /warehouse/{warehouseId}/materials
```

#### 2.2 更新仓库物资配置
```
PUT /warehouse/{warehouseId}/materials
Content-Type: application/json

["material_001", "material_002", "material_003"]
```

#### 2.3 添加单个物资
```
POST /warehouse/{warehouseId}/material/{materialId}
```

#### 2.4 移除单个物资
```
DELETE /warehouse/{warehouseId}/material/{materialId}
```

### 3. 辅助接口

#### 3.1 导出仓库数据
```
POST /warehouse/export
```

## 权限配置

### 权限标识
- `warehouse:list` - 查询仓库列表
- `warehouse:query` - 查询仓库详情
- `warehouse:add` - 新增仓库
- `warehouse:edit` - 修改仓库
- `warehouse:remove` - 删除仓库
- `warehouse:export` - 导出仓库

## 字典配置

### 仓库类型 (warehouse_type)
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, remark) 
VALUES ('仓库类型', 'warehouse_type', '0', '仓库类型字典');

INSERT INTO sys_dict_data (dict_type, dict_label, dict_value, dict_sort, status) VALUES
('warehouse_type', '综合仓库', '1', 1, '0'),
('warehouse_type', '专用仓库', '2', 2, '0'),
('warehouse_type', '临时仓库', '3', 3, '0'),
('warehouse_type', '中转仓库', '4', 4, '0');
```

## 新增仓库请求示例

### 完整请求示例
```json
{
  "warehouseName": "南宁市中心应急仓库",
  "warehouseType": "1",
  "belongOrgCode": "NNEM001",
  "belongOrgName": "南宁市应急管理局",
  "address": "南宁市青秀区民族大道200号应急管理大楼后院",
  "principal": "张仓管",
  "contactPhone": "13800138000",
  "roadCode": "G72",
  "stake": "K100+500",
  "latitude": "22.817002",
  "longitude": "108.366543",
  "remark": "市级中心应急物资仓库，负责全市应急物资储备和调配",
  "materials": [
    {
      "materialName": "应急帐篷",
      "materialType": "0",
      "specModel": "3x3米防水帐篷",
      "categoryCode": "SHELTER",
      "quantity": 200,
      "unit": "顶",
      "expiryDate": "2025-12-31",
      "remark": "防水防风应急帐篷，适用于灾民临时安置"
    },
    {
      "materialName": "应急食品",
      "materialType": "0",
      "specModel": "压缩饼干500g装",
      "categoryCode": "FOOD",
      "quantity": 1000,
      "unit": "包",
      "expiryDate": "2025-06-30",
      "remark": "高能量压缩食品，保质期2年"
    },
    {
      "materialName": "发电机",
      "materialType": "1",
      "specModel": "5KW汽油发电机",
      "categoryCode": "POWER",
      "quantity": 15,
      "unit": "台",
      "remark": "应急供电设备，可连续工作8小时"
    },
    {
      "materialName": "救援照明灯",
      "materialType": "1",
      "specModel": "LED强光照明灯",
      "categoryCode": "LIGHTING",
      "quantity": 50,
      "unit": "个",
      "remark": "便携式强光照明设备，续航12小时"
    }
  ]
}
```

## 业务逻辑说明

### 1. 数据创建流程
1. **创建仓库记录**：在 `em_warehouse` 表中插入仓库基础信息
2. **创建专属物资**：遍历 `materials` 数组，为每个物资创建记录
   - 在 `em_material` 表中插入物资记录
   - 设置 `warehouse_id` 字段标识归属仓库
   - 在 `em_warehouse_material` 表中创建关联关系

### 2. 字段自动设置
- **仓库ID**：系统自动生成UUID
- **物资ID**：系统自动生成UUID
- **状态**：默认设置为1（正常）
- **创建信息**：自动设置创建人和创建时间
- **删除标志**：默认设置为0（未删除）

### 3. 事务保证
整个创建过程在一个事务中执行，确保数据一致性。

## 注意事项

1. **物资归属**：创建的物资都归属于该仓库，`warehouse_id`字段会被设置
2. **数据完整性**：建议前端进行基础验证，确保必填字段不为空
3. **权限控制**：需要相应权限才能调用接口
4. **地理位置**：经纬度信息用于地图定位和距离计算

这个仓库管理功能提供了完整的仓库信息维护、物资配置、数据导出等功能！
