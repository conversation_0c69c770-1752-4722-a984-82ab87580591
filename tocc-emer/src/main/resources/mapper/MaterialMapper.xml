<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.MaterialMapper">
    
    <insert id="insertMaterial" parameterType="com.tocc.domain.dto.MaterialDTO">
        insert into em_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialName != null">material_name,</if>
            <if test="materialType != null">material_type,</if>
            <if test="specModel != null">spec_model,</if>
            <if test="categoryCode != null">category_code,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unit != null">unit,</if>
            <if test="status != null">status,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="remark != null">remark,</if>
            <if test="teamId != null">team_id,</if>
            <if test="creator != null">creator,</if>
            <if test="delFlag != null">del_flag,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="specModel != null">#{specModel},</if>
            <if test="categoryCode != null">#{categoryCode},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unit != null">#{unit},</if>
            <if test="status != null">#{status},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="delFlag != null">#{delFlag},</if>
            now(),
         </trim>
    </insert>

</mapper>
