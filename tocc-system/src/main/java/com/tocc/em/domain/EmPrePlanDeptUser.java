package com.tocc.em.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 预案组织体系人员对象 em_pre_plan_dept_user
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmPrePlanDeptUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID主键 */

    private String id;

    /** 组织机构ID(关联em_pre_plan_dept表) */
    @Excel(name = "组织机构ID(关联em_pre_plan_dept表)")
    private String emDeptId;

    /** 角色ID */
    @Excel(name = "角色ID")
    private String roleId;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    private String leaderName;

    @Excel(name = "负责人id")
    private String leaderId;

    /** 所属部门（系统单位ID） */
    @Excel(name = "所属部门", readConverterExp = "系=统单位ID")
    private String depId;

    /** 职务/岗位 */
    @Excel(name = "职务/岗位")
    private String position;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在1删除) */
    @Excel(name = "删除标志(0存在1删除)")
    private Integer delFlag;


}
