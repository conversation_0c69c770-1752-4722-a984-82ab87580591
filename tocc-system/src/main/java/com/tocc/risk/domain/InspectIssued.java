package com.tocc.risk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 检查下发对象 risk_inspect_issued
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "检查下发对象")
public class InspectIssued extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 所属隐患ID */
    @Excel(name = "所属项目ID")
    @ApiModelProperty(value = "所属项目ID")
    private Long projectsId;

    /** 检查名称 */
    @Excel(name = "检查名称")
    @ApiModelProperty(value = "检查名称")
    private String name;

    /** 检查类别（表risk_inspect_type） */
    @Excel(name = "检查类别", readConverterExp = "表=risk_inspect_type")
    @ApiModelProperty(value = "检查类别")
    private String type;

    /** 任务单位逗号隔开 */
    @Excel(name = "任务单位逗号隔开")
    @ApiModelProperty(value = "任务单位逗号隔开")
    private String units;

    /** 任务单位ID逗号隔开 */
    @Excel(name = "任务单位ID逗号隔开")
    @ApiModelProperty(value = "任务单位ID逗号隔开")
    private String unitId;

    @ApiModelProperty(value = "任务填报人")
    private String fillName;

    @ApiModelProperty(value = "任务填报人ID")
    private String fillId;

    /** 填报项（表risk_fill_fields的ID逗号隔开） */
    @Excel(name = "填报项", readConverterExp = "表=risk_fill_fields的ID逗号隔开")
    @ApiModelProperty(value = "填报项")
    private String fields;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "截止时间")
    private Date endTime;

    /** 状态（1-进行中，2-已完成） */
    @Excel(name = "状态", readConverterExp = "1=-进行中，2-已完成")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /** 下发单位名称 */
    @Excel(name = "下发单位名称")
    @ApiModelProperty(value = "下发单位名称")
    private String issuedUnit;

    /** 下发单位ID */
    @Excel(name = "下发单位ID")
    @ApiModelProperty(value = "下发单位ID")
    private Long issuedUnitId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "创建人ID")
    private Integer delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProjectsId(Long projectsId)
    {
        this.projectsId = projectsId;
    }

    public Long getProjectsId()
    {
        return projectsId;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setUnits(String units) 
    {
        this.units = units;
    }

    public String getUnits() 
    {
        return units;
    }

    public void setUnitId(String unitId) 
    {
        this.unitId = unitId;
    }

    public String getUnitId() 
    {
        return unitId;
    }

    public void setFillName(String fillName)
    {
        this.fillName = fillName;
    }

    public String getFillName()
    {
        return fillName;
    }

    public void setFillId(String FillId)
    {
        this.fillId = FillId;
    }

    public String getFillId()
    {
        return fillId;
    }

    public void setFields(String fields) 
    {
        this.fields = fields;
    }

    public String getFields() 
    {
        return fields;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    public void setIssuedUnit(String issuedUnit) 
    {
        this.issuedUnit = issuedUnit;
    }

    public String getIssuedUnit() 
    {
        return issuedUnit;
    }

    public void setIssuedUnitId(Long issuedUnitId) 
    {
        this.issuedUnitId = issuedUnitId;
    }

    public Long getIssuedUnitId() 
    {
        return issuedUnitId;
    }

    public void setCreateById(Long createById) 
    {
        this.createById = createById;
    }

    public Long getCreateById() 
    {
        return createById;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pitfallsId", getProjectsId())
            .append("name", getName())
            .append("type", getType())
            .append("units", getUnits())
            .append("unitId", getUnitId())
            .append("fillName", getFillName())
            .append("fillId", getFillId())
            .append("fields", getFields())
            .append("endTime", getEndTime())
            .append("status", getStatus())
            .append("remarks", getRemarks())
            .append("issuedUnit", getIssuedUnit())
            .append("issuedUnitId", getIssuedUnitId())
            .append("createBy", getCreateBy())
            .append("createById", getCreateById())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
