<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmPrePlanDeptMapper">

    <resultMap type="EmPrePlanDept" id="EmPrePlanDeptResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="version"    column="version"    />
        <result property="prePlanId"    column="pre_plan_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="deptLevel"    column="dept_level"    />
        <result property="deptJob"    column="dept_job"    />
        <result property="eventLevel"    column="event_level"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmPrePlanDeptVo">
        select id, parent_id, version, pre_plan_id, dept_name, dept_level, dept_job, event_level, create_time, creator, update_time, updater, del_flag from em_pre_plan_dept
    </sql>

    <select id="selectEmPrePlanDeptList" parameterType="EmPrePlanDept" resultMap="EmPrePlanDeptResult">
        <include refid="selectEmPrePlanDeptVo"/>
        <where>
            del_flag = 0
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="prePlanId != null  and prePlanId != ''"> and pre_plan_id = #{prePlanId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="deptLevel != null "> and dept_level = #{deptLevel}</if>
            <if test="deptJob != null  and deptJob != ''"> and dept_job = #{deptJob}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectEmPrePlanDeptById" parameterType="String" resultMap="EmPrePlanDeptResult">
        <include refid="selectEmPrePlanDeptVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmPrePlanDept" parameterType="EmPrePlanDept">
        insert into em_pre_plan_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="version != null">version,</if>
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="deptLevel != null">dept_level,</if>
            <if test="deptJob != null and deptJob != ''">dept_job,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="version != null">#{version},</if>
            <if test="prePlanId != null and prePlanId != ''">#{prePlanId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="deptLevel != null">#{deptLevel},</if>
            <if test="deptJob != null and deptJob != ''">#{deptJob},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmPrePlanDept" parameterType="EmPrePlanDept">
        update em_pre_plan_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id = #{prePlanId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="deptLevel != null">dept_level = #{deptLevel},</if>
            <if test="deptJob != null and deptJob != ''">dept_job = #{deptJob},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmPrePlanDeptById" parameterType="String">
        update  em_pre_plan_dept set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmPrePlanDeptByIds" parameterType="String">
        delete from em_pre_plan_dept where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
