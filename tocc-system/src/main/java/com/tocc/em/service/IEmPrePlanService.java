package com.tocc.em.service;

import java.util.List;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.dto.EmPrePlanDTO;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.vo.EmPrePlanVO;

/**
 * 应急预案数据Service接口
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IEmPrePlanService
{
    /**
     * 查询应急预案数据
     *
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    public EmPrePlanVO selectEmPrePlanById(String id);

    /**
     * 查询应急预案数据列表
     *
     * @param emPrePlanQO 应急预案数据
     * @return 应急预案数据集合
     */
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlanQO );

    /**
     * 新增应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    public int insertEmPrePlan( EmPrePlanDTO emPrePlanDTO);

    /**
     * 修改应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    public int updateEmPrePlan(EmPrePlanDTO emPrePlanDTO);

    /**
     * 批量删除应急预案数据
     *
     * @param ids 需要删除的应急预案数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanByIds(String[] ids);

    /**
     * 删除应急预案数据信息
     *
     * @param id 应急预案数据主键
     * @return 结果
     */
    public int deleteEmPrePlanById(String id);
}
