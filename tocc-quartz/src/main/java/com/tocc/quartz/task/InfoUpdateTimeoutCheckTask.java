package com.tocc.quartz.task;

import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.EmPrePlanVO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.service.IAlarmService;
import com.tocc.service.ISysDictDataService;
import com.tocc.service.IEmPrePlanService;
import com.tocc.service.IRescueTeamService;
import com.tocc.service.IWarehouseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * 信息更新超时检查定时任务
 * 
 * <AUTHOR>
 */
@Component("infoUpdateTimeoutCheckTask")
public class InfoUpdateTimeoutCheckTask {
    
    private static final Logger log = LoggerFactory.getLogger(InfoUpdateTimeoutCheckTask.class);
    
    @Autowired
    private IAlarmService alarmService;
    
    @Autowired
    private ISysDictDataService dictDataService;
    
    @Autowired
    private IEmPrePlanService prePlanService;
    
    @Autowired
    private IRescueTeamService rescueTeamService;
    
    @Autowired
    private IWarehouseService warehouseService;
    
    /**
     * 执行信息更新超时检查
     * 
     * @param params 参数（可选）
     */
    public void execute(String params) {
        log.info("开始执行信息更新超时检查任务，参数：{}", params);
        
        try {
            // 检查应急预案
            checkPrePlanTimeout();

            // 检查救援队伍
            checkRescueTeamTimeout();

            // 检查物资仓库
            checkWarehouseTimeout();

            log.info("信息更新超时检查任务执行完成");
        } catch (Exception e) {
            log.error("信息更新超时检查任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 检查应急预案更新超时
     */
    private void checkPrePlanTimeout() {
        // 获取超时阈值（分钟）
        int timeoutMinutes = getTimeoutMinutes("应急预案更新阈值", 1);
        
        // 计算超时时间点
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        // 查询超时的预案
        List<EmPrePlanVO> timeoutPlans = prePlanService.selectTimeoutPlans(timeoutTime);
        
        log.info("发现{}个超时的应急预案", timeoutPlans.size());
        
        // 为每个超时预案创建告警
        for (EmPrePlanVO plan : timeoutPlans) {
            createTimeoutAlarm("应急预案", plan.getId(), plan.getPlanName(), 
                              plan.getUpdateTime(), timeoutMinutes, plan.getCompilingDept());
        }
    }
    
    /**
     * 检查救援队伍更新超时
     */
    private void checkRescueTeamTimeout() {
        int timeoutMinutes = getTimeoutMinutes("救援队伍更新阈值", 1);
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        List<RescueTeamVO> timeoutTeams = rescueTeamService.selectTimeoutTeams(timeoutTime);
        
        log.info("发现{}个超时的救援队伍", timeoutTeams.size());
        
        for (RescueTeamVO team : timeoutTeams) {
            createTimeoutAlarm("救援队伍", team.getId(), team.getTeamName(), 
                              team.getUpdateTime(), timeoutMinutes, team.getJurisdictionUnit());
        }
    }
    
    /**
     * 检查物资仓库更新超时
     */
    private void checkWarehouseTimeout() {
        int timeoutMinutes = getTimeoutMinutes("物资仓库更新阈值", 1);
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        List<WarehouseVO> timeoutWarehouses = warehouseService.selectTimeoutWarehouses(timeoutTime);
        
        log.info("发现{}个超时的物资仓库", timeoutWarehouses.size());
        
        for (WarehouseVO warehouse : timeoutWarehouses) {
            createTimeoutAlarm("物资仓库", warehouse.getId(), warehouse.getWarehouseName(), 
                              warehouse.getUpdateTime(), timeoutMinutes, warehouse.getBelongOrgName());
        }
    }
    
    /**
     * 获取超时阈值（分钟）
     */
    private int getTimeoutMinutes(String dictLabel, int defaultValue) {
        try {
            String dictValue = dictDataService.selectDictLabel("info_update_timeout", dictLabel);
            if (StringUtils.isNotEmpty(dictValue)) {
                return Integer.parseInt(dictValue);
            }
        } catch (Exception e) {
            log.warn("获取字典值失败，使用默认值：{}", defaultValue, e);
        }
        return defaultValue;
    }
    
    /**
     * 创建超时告警
     */
    private void createTimeoutAlarm(String infoType, String infoId, String infoName, 
                                   Date lastUpdateTime, int timeoutMinutes, String orgName) {
        
        // 检查是否已经存在相同的告警（避免重复告警）
        if (alarmService.existsTimeoutAlarm(infoType, infoId)) {
            log.debug("{}[{}]已存在超时告警，跳过", infoType, infoName);
            return;
        }
        
        // 计算超时分钟数
        long overdueMinutes = ChronoUnit.MINUTES.between(
            lastUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        ) - timeoutMinutes;
        
        // 构建告警标题
        String alarmTitle = String.format("%s信息更新超时", infoType);
        
        // 构建告警内容
        String alarmContent = String.format(
            "%s\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。请及时更新相关信息以确保数据准确性。",
            infoType, infoName, timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, lastUpdateTime)
        );
        
        // 创建告警记录
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmType("信息更新超时");
        alarmInfo.setAlarmTitle(alarmTitle);
        alarmInfo.setAlarmContent(alarmContent);
        alarmInfo.setAlarmLevel("2"); // 中等级别
        alarmInfo.setRelatedType(infoType);
        alarmInfo.setRelatedId(infoId);
        alarmInfo.setOrgName(orgName);
        alarmInfo.setAlarmTime(new Date());
        alarmInfo.setStatus("0"); // 未处理
        
        alarmService.createAlarm(alarmInfo);
        
        log.info("创建{}更新超时告警：{}", infoType, infoName);
    }
}
