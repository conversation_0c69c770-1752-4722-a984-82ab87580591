<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.ApproveMapper">
    
    <resultMap type="Approve" id="ApproveResult">
        <result property="id"    column="id"    />
        <result property="pitfallsId"    column="pitfalls_id"    />
        <result property="issuedId"    column="issued_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="status"    column="status"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveById"    column="approve_by_id"    />
        <result property="approveTime"    column="approve_time"    />
        <result property="provinceBy"    column="province_by"    />
        <result property="provinceById"    column="province_by_id"    />
        <result property="provinceTime"    column="province_time"    />
    </resultMap>

    <sql id="selectApproveVo">
        select id, pitfalls_id, issued_id, task_id, status, approve_by, approve_by_id, approve_time, province_by, province_by_id, province_time from risk_approve
    </sql>

    <select id="selectApproveList" parameterType="Approve" resultMap="ApproveResult">
        <include refid="selectApproveVo"/>
        <where>  
            <if test="pitfallsId != null "> and pitfalls_id = #{pitfallsId}</if>
            <if test="issuedId != null "> and issued_id = #{issuedId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="approveBy != null  and approveBy != ''"> and approve_by = #{approveBy}</if>
            <if test="approveById != null "> and approve_by_id = #{approveById}</if>
            <if test="approveTime != null "> and approve_time = #{approveTime}</if>
            <if test="provinceBy != null  and provinceBy != ''"> and province_by = #{provinceBy}</if>
            <if test="provinceById != null "> and province_by_id = #{provinceById}</if>
            <if test="provinceTime != null "> and province_time = #{provinceTime}</if>
        </where>
    </select>
    
    <select id="selectApproveById" parameterType="Long" resultMap="ApproveResult">
        <include refid="selectApproveVo"/>
        where id = #{id}
    </select>

    <insert id="insertApprove" parameterType="Approve">
        insert into risk_approve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pitfallsId != null">pitfalls_id,</if>
            <if test="issuedId != null">issued_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="status != null">status,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveById != null">approve_by_id,</if>
            <if test="approveTime != null">approve_time,</if>
            <if test="provinceBy != null">province_by,</if>
            <if test="provinceById != null">province_by_id,</if>
            <if test="provinceTime != null">province_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pitfallsId != null">#{pitfallsId},</if>
            <if test="issuedId != null">#{issuedId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="status != null">#{status},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveById != null">#{approveById},</if>
            <if test="approveTime != null">#{approveTime},</if>
            <if test="provinceBy != null">#{provinceBy},</if>
            <if test="provinceById != null">#{provinceById},</if>
            <if test="provinceTime != null">#{provinceTime},</if>
         </trim>
    </insert>

    <update id="updateApprove" parameterType="Approve">
        update risk_approve
        <trim prefix="SET" suffixOverrides=",">
            <if test="pitfallsId != null">pitfalls_id = #{pitfallsId},</if>
            <if test="issuedId != null">issued_id = #{issuedId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveById != null">approve_by_id = #{approveById},</if>
            <if test="approveTime != null">approve_time = #{approveTime},</if>
            <if test="provinceBy != null">province_by = #{provinceBy},</if>
            <if test="provinceById != null">province_by_id = #{provinceById},</if>
            <if test="provinceTime != null">province_time = #{provinceTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApproveById" parameterType="Long">
        delete from risk_approve where id = #{id}
    </delete>

    <delete id="deleteApproveByIds" parameterType="String">
        delete from risk_approve where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>