package com.tocc.common.map.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.map.domain.MapVO;
import com.tocc.common.map.service.IMapService;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;

@Service("mapService")
public class MapServiceImpl implements IMapService {

    private static String KEY= "50b02607ff4943ab7d3ac53c49fee7d6";

    public  static String GD_URL= "https://restapi.amap.com/v3/geocode/geo?address=%s&key=%s";

    // 地址转经纬度
    public MapVO getLonAndLatByAddress(String address) {
        MapVO mapVO = new MapVO();
        try {
            GD_URL = String.format(GD_URL, address, KEY);
            //高德接口返回的是JSON格式的字符串
            String queryResult = getResponse(GD_URL);
            JSONObject obj = JSONObject.parseObject(queryResult);

            if(String.valueOf(obj.get("status")).equals("1")){
                // 获取geocodes数组
                Object geocodesObj = obj.get("geocodes");
                if (geocodesObj != null) {
                    String geocodesStr = geocodesObj.toString();
                    // 如果是数组格式，取第一个结果
                    if (geocodesStr.startsWith("[") && geocodesStr.endsWith("]")) {
                        // 解析数组，取第一个元素
                        com.alibaba.fastjson2.JSONArray geocodesArray = obj.getJSONArray("geocodes");
                        if (geocodesArray != null && geocodesArray.size() > 0) {
                            JSONObject firstGeocode = geocodesArray.getJSONObject(0);
                            String location = String.valueOf(firstGeocode.get("location"));
                            // location中逗号前面为经度，后面为纬度
                            String[] parts = location.split(",");
                            mapVO.setLng(parts[0].trim());
                            mapVO.setLat(parts[1].trim());
                        } else {
                            throw new RuntimeException("地址转换经纬度失败：未找到有效的地理编码结果");
                        }
                    } else {
                        // 如果不是数组格式，按原来的方式处理
                        JSONObject jobJSON = JSONObject.parseObject(geocodesStr);
                        String location = String.valueOf(jobJSON.get("location"));
                        String[] parts = location.split(",");
                        mapVO.setLng(parts[0].trim());
                        mapVO.setLat(parts[1].trim());
                    }
                } else {
                    throw new RuntimeException("地址转换经纬度失败：geocodes为空");
                }
            } else {
                throw new RuntimeException("地址转换经纬度失败，错误码：" + obj.get("infocode"));
            }
        } catch (Exception e) {
            // 记录详细错误信息
            System.err.println("地址转换经纬度异常，地址：" + address + "，错误：" + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("地址转换经纬度失败：" + e.getMessage());
        }

        return mapVO;
    }

    private static String getResponse(String serverUrl) {
        // 用JAVA发起http请求，并返回json格式的结果
        StringBuffer result = new StringBuffer();
        try {
            URL url = new URL(serverUrl);
            URLConnection conn = url.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

}
