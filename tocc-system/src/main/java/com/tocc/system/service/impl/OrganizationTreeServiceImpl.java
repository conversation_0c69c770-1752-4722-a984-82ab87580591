package com.tocc.system.service.impl;

import com.tocc.system.domain.vo.OrganizationTreeVO;
import com.tocc.system.mapper.OrganizationTreeMapper;
import com.tocc.system.service.IOrganizationTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织架构树形结构 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class OrganizationTreeServiceImpl implements IOrganizationTreeService {
    
    @Autowired
    private OrganizationTreeMapper organizationTreeMapper;
    
    /**
     * 获取完整的组织架构树形结构
     */
    @Override
    public List<OrganizationTreeVO> getOrganizationTree(Long deptId) {
        // 查询所有组织架构数据
        List<OrganizationTreeVO> allNodes = organizationTreeMapper.selectOrganizationTree(deptId);
        
        // 构建树形结构
        return buildTree(allNodes);
    }
    
    /**
     * 获取指定部门的组织架构树形结构
     */
    @Override
    public List<OrganizationTreeVO> getDepartmentTree(Long deptId) {
        if (deptId == null) {
            return getOrganizationTree(null);
        }
        
        List<OrganizationTreeVO> result = new ArrayList<>();
        
        // 1. 获取部门信息
        List<OrganizationTreeVO> depts = organizationTreeMapper.selectDeptList(deptId);
        if (depts.isEmpty()) {
            return result;
        }
        
        OrganizationTreeVO dept = depts.get(0);
        
        // 2. 获取部门下的岗位
        List<OrganizationTreeVO> posts = organizationTreeMapper.selectPostsByDeptId(deptId);
        
        // 3. 为每个岗位获取用户
        for (OrganizationTreeVO post : posts) {
            String postIdStr = post.getId().replace("post_", "");
            Long postId = Long.valueOf(postIdStr);
            List<OrganizationTreeVO> users = organizationTreeMapper.selectUsersByDeptAndPost(deptId, postId);
            post.setChildren(users);
        }
        
        // 4. 获取没有岗位的用户（直接挂在部门下）
        List<OrganizationTreeVO> directUsers = organizationTreeMapper.selectUsersByDeptAndPost(deptId, null);
        // 过滤掉已经有岗位的用户
        Set<String> usersWithPosts = posts.stream()
                .flatMap(post -> post.getChildren().stream())
                .map(OrganizationTreeVO::getId)
                .collect(Collectors.toSet());
        
        directUsers = directUsers.stream()
                .filter(user -> !usersWithPosts.contains(user.getId()))
                .collect(Collectors.toList());
        
        // 5. 组装树形结构
        List<OrganizationTreeVO> children = new ArrayList<>();
        children.addAll(posts);
        children.addAll(directUsers);
        
        dept.setChildren(children);
        result.add(dept);
        
        return result;
    }
    
    /**
     * 构建树形结构
     */
    @Override
    public List<OrganizationTreeVO> buildTree(List<OrganizationTreeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建ID到节点的映射
        Map<String, OrganizationTreeVO> nodeMap = new HashMap<>();
        for (OrganizationTreeVO node : nodes) {
            nodeMap.put(node.getId(), node);
        }
        
        // 构建树形结构
        List<OrganizationTreeVO> rootNodes = new ArrayList<>();
        
        for (OrganizationTreeVO node : nodes) {
            String parentId = node.getParentId();
            
            if (parentId == null || parentId.isEmpty()) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                OrganizationTreeVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.addChild(node);
                } else {
                    // 如果找不到父节点，也作为根节点处理
                    rootNodes.add(node);
                }
            }
        }
        
        // 对每个节点的子节点进行排序
        sortChildren(rootNodes);
        
        return rootNodes;
    }
    
    /**
     * 获取部门下的岗位和人员信息
     */
    @Override
    public List<OrganizationTreeVO> getDepartmentPostsAndUsers(Long deptId) {
        List<OrganizationTreeVO> result = new ArrayList<>();
        
        // 获取岗位
        List<OrganizationTreeVO> posts = organizationTreeMapper.selectPostsByDeptId(deptId);
        
        // 为每个岗位获取用户
        for (OrganizationTreeVO post : posts) {
            String postIdStr = post.getId().replace("post_", "");
            Long postId = Long.valueOf(postIdStr);
            List<OrganizationTreeVO> users = organizationTreeMapper.selectUsersByDeptAndPost(deptId, postId);
            post.setChildren(users);
        }
        
        result.addAll(posts);
        
        // 获取没有岗位的用户
        List<OrganizationTreeVO> directUsers = organizationTreeMapper.selectUsersByDeptAndPost(deptId, null);
        // 过滤掉已经有岗位的用户
        Set<String> usersWithPosts = posts.stream()
                .flatMap(post -> post.getChildren().stream())
                .map(OrganizationTreeVO::getId)
                .collect(Collectors.toSet());
        
        directUsers = directUsers.stream()
                .filter(user -> !usersWithPosts.contains(user.getId()))
                .collect(Collectors.toList());
        
        result.addAll(directUsers);
        
        return result;
    }
    
    /**
     * 递归排序子节点
     */
    private void sortChildren(List<OrganizationTreeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        // 按类型和排序号排序
        nodes.sort((a, b) -> {
            // 先按类型排序：dept -> post -> user
            int typeCompare = getTypeOrder(a.getType()) - getTypeOrder(b.getType());
            if (typeCompare != 0) {
                return typeCompare;
            }
            
            // 再按排序号排序
            Integer orderA = a.getOrderNum();
            Integer orderB = b.getOrderNum();
            if (orderA != null && orderB != null) {
                return orderA.compareTo(orderB);
            } else if (orderA != null) {
                return -1;
            } else if (orderB != null) {
                return 1;
            }
            
            // 最后按名称排序
            return a.getName().compareTo(b.getName());
        });
        
        // 递归排序子节点
        for (OrganizationTreeVO node : nodes) {
            sortChildren(node.getChildren());
        }
    }
    
    /**
     * 获取类型排序权重
     */
    private int getTypeOrder(String type) {
        switch (type) {
            case "dept": return 1;
            case "post": return 2;
            case "user": return 3;
            default: return 4;
        }
    }
}
