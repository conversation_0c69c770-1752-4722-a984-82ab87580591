<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmEventLevelMapper">

    <resultMap type="EmEventLevel" id="EmEventLevelResult">
        <result property="id"    column="id"    />
        <result property="prePlanId"    column="pre_plan_id"    />
        <result property="version"    column="version"    />
        <result property="eventLevel"    column="event_level"    />
        <result property="conditions"    column="conditions"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmEventLevelVo">
        select id, pre_plan_id, version, event_level, conditions, create_time, creator, update_time, updater, del_flag from em_event_level
    </sql>

    <select id="selectEmEventLevelList" parameterType="EmEventLevel" resultMap="EmEventLevelResult">
        <include refid="selectEmEventLevelVo"/>
        <where>
            del_flag = 0
            <if test="prePlanId != null  and prePlanId != ''"> and pre_plan_id = #{prePlanId}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="eventLevel != null "> and event_level = #{eventLevel}</if>
            <if test="conditions != null  and conditions != ''"> and conditions = #{conditions}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectEmEventLevelById" parameterType="Long" resultMap="EmEventLevelResult">
        <include refid="selectEmEventLevelVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmEventLevel" parameterType="EmEventLevel">
        insert into em_event_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id,</if>
            <if test="version != null">version,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="conditions != null and conditions != ''">conditions,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="prePlanId != null and prePlanId != ''">#{prePlanId},</if>
            <if test="version != null">#{version},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="conditions != null and conditions != ''">#{conditions},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmEventLevel" parameterType="EmEventLevel">
        update em_event_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id = #{prePlanId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="conditions != null and conditions != ''">conditions = #{conditions},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmEventLevelById" parameterType="String">
        update  em_event_level  set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmEventLevelByIds" parameterType="String">
        delete from em_event_level where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
