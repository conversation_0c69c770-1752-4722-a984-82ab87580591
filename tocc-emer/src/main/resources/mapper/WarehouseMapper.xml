<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.WarehouseMapper">
    
    <resultMap type="com.tocc.domain.vo.WarehouseVO" id="WarehouseResult">
        <result property="id"                column="id"                />
        <result property="warehouseName"     column="warehouse_name"    />
        <result property="warehouseType"     column="warehouse_type"    />
        <result property="warehouseTypeName" column="warehouse_type_name" />
        <result property="belongOrgCode"     column="belong_org_code"   />
        <result property="belongOrgName"     column="belong_org_name"   />
        <result property="address"           column="address"           />
        <result property="principal"         column="principal"         />
        <result property="contactPhone"      column="contact_phone"     />
        <result property="roadCode"          column="road_code"         />
        <result property="stake"             column="stake"             />
        <result property="latitude"          column="latitude"          />
        <result property="longitude"         column="longitude"         />
        <result property="remark"            column="remark"            />
        <result property="createTime"        column="create_time"       />
        <result property="creator"           column="creator"           />
        <result property="updateTime"        column="update_time"       />
        <result property="updater"           column="updater"           />
        <result property="totalMaterialCount" column="total_material_count" />
    </resultMap>

    <sql id="selectWarehouseVo">
        select 
            w.id, w.warehouse_name, w.warehouse_type, w.belong_org_code, w.belong_org_name,
            w.address, w.principal, w.contact_phone, w.road_code, w.stake,
            w.latitude, w.longitude, w.remark, w.create_time, w.creator, w.update_time, w.updater,
            -- 字典值转换
            dt1.dict_label as warehouse_type_name,
            -- 统计物资数量
            COALESCE(mc.material_count, 0) as total_material_count
        from em_warehouse w
        left join sys_dict_data dt1 on dt1.dict_value = w.warehouse_type and dt1.dict_type = 'warehouse_type'
        left join (
            select warehouse_id, count(*) as material_count
            from em_warehouse_material
            group by warehouse_id
        ) mc on w.id = mc.warehouse_id
    </sql>

    <select id="selectWarehouseList" parameterType="com.tocc.domain.dto.WarehouseDTO" resultMap="WarehouseResult">
        <include refid="selectWarehouseVo"/>
        <where>
            w.del_flag = 0
            <if test="warehouseName != null and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="warehouseType != null and warehouseType != ''"> and w.warehouse_type = #{warehouseType}</if>
            <if test="belongOrgCode != null and belongOrgCode != ''"> and w.belong_org_code = #{belongOrgCode}</if>
            <if test="belongOrgName != null and belongOrgName != ''"> and w.belong_org_name like concat('%', #{belongOrgName}, '%')</if>
            <if test="address != null and address != ''"> and w.address like concat('%', #{address}, '%')</if>
            <if test="principal != null and principal != ''"> and w.principal like concat('%', #{principal}, '%')</if>
            <if test="contactPhone != null and contactPhone != ''"> and w.contact_phone = #{contactPhone}</if>
            <if test="roadCode != null and roadCode != ''"> and w.road_code = #{roadCode}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(w.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(w.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by w.create_time desc
    </select>
    
    <select id="selectWarehouseById" parameterType="String" resultMap="WarehouseResult">
        <include refid="selectWarehouseVo"/>
        where w.id = #{id} and w.del_flag = 0
    </select>
        
    <insert id="insertWarehouse" parameterType="com.tocc.domain.dto.WarehouseDTO">
        insert into em_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="warehouseType != null">warehouse_type,</if>
            <if test="belongOrgCode != null">belong_org_code,</if>
            <if test="belongOrgName != null">belong_org_name,</if>
            <if test="address != null">address,</if>
            <if test="principal != null">principal,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="roadCode != null">road_code,</if>
            <if test="stake != null">stake,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="warehouseType != null">#{warehouseType},</if>
            <if test="belongOrgCode != null">#{belongOrgCode},</if>
            <if test="belongOrgName != null">#{belongOrgName},</if>
            <if test="address != null">#{address},</if>
            <if test="principal != null">#{principal},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="roadCode != null">#{roadCode},</if>
            <if test="stake != null">#{stake},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            now(),
         </trim>
    </insert>

    <update id="updateWarehouse" parameterType="com.tocc.domain.dto.WarehouseDTO">
        update em_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="warehouseType != null">warehouse_type = #{warehouseType},</if>
            <if test="belongOrgCode != null">belong_org_code = #{belongOrgCode},</if>
            <if test="belongOrgName != null">belong_org_name = #{belongOrgName},</if>
            <if test="address != null">address = #{address},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="roadCode != null">road_code = #{roadCode},</if>
            <if test="stake != null">stake = #{stake},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updater != null">updater = #{updater},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWarehouseById" parameterType="String">
        update em_warehouse set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteWarehouseByIds" parameterType="String">
        update em_warehouse set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
