package com.tocc.web.mapController;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.map.service.IMapService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/map")
public class MapController extends BaseController {

    @Autowired
    private IMapService mapService;
    
    @ApiOperation("地址转经纬度")
    @PostMapping("/getLonAndLatByAddress")
    public AjaxResult getLonAndLatByAddress(@RequestParam("address") String address) {
        Object result = mapService.getLonAndLatByAddress(address);
        return success(result);
    }

}
