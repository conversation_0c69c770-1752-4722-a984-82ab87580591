package com.tocc.risk.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 隐患列对象 risk_pitfalls
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "隐患列对象")
public class Pitfalls extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "表ID")
    private Long id;

    /** 表ID */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String city;

    /** 区/县 */
    @Excel(name = "区/县")
    @ApiModelProperty(value = "区/县")
    private String district;

    /** 坐标经度 */
    @Excel(name = "坐标经度")
    @ApiModelProperty(value = "坐标经度")
    private String lot;

    /** 坐标纬度 */
    @Excel(name = "坐标纬度")
    @ApiModelProperty(value = "坐标纬度")
    private String lat;

    /** 所属单位 */
    @Excel(name = "所属单位")
    @ApiModelProperty(value = "所属单位")
    private String units;

    /** 检查类型 */
    @Excel(name = "检查类型")
    @ApiModelProperty(value = "检查类型")
    private String inspectType;

    /** 路段编号 */
    @Excel(name = "路段编号")
    @ApiModelProperty(value = "路段编号")
    private String roadNum;

    /** 风险等级（1-高，2-中，3-低） */
    @Excel(name = "风险等级", readConverterExp = "1=-高，2-中，3-低")
    @ApiModelProperty(value = "风险等级")
    private Integer riskLevel;

    /** 是否隐患点（1-是，0-否） */
    @Excel(name = "是否隐患点", readConverterExp = "1=-是，0-否")
    @ApiModelProperty(value = "是否隐患点")
    private Integer isPitfalls;

    /** 起点桩号 */
    @Excel(name = "起点桩号")
    @ApiModelProperty(value = "起点桩号")
    private String pileStart;

    /** 止点桩号 */
    @Excel(name = "止点桩号")
    @ApiModelProperty(value = "止点桩号")
    private String pileEnd;

    /** 是否已采取措施（0-否，1-是） */
    @Excel(name = "是否已采取措施（0-否，1-是）", readConverterExp = "是否已采取措施（0-否，1-是）")
    @ApiModelProperty(value = "是否已采取措施（0-否，1-是）")
    private String isMeasure;

    /** 已（拟）采取的措施 */
    @Excel(name = "已（拟）采取的措施", readConverterExp = "已（拟）采取的措施")
    @ApiModelProperty(value = "已（拟）采取的措施")
    private String measure;

    /** 现场照片/附件 */
    @Excel(name = "现场照片/附件")
    @ApiModelProperty(value = "现场照片/附件")
    private String sceneImg;

    /** 措施附件 */
    @Excel(name = "措施附件")
    @ApiModelProperty(value = "措施附件")
    private String measureFiles;

    /** 省级责任单位 */
    @Excel(name = "省级责任单位")
    @ApiModelProperty(value = "省级责任单位")
    private String provinceUnit;

    /** 复核责任单位 */
    @Excel(name = "复核责任单位")
    @ApiModelProperty(value = "复核责任单位")
    private String reviewUnit;

    /** 排查责任单位 */
    @Excel(name = "排查责任单位")
    @ApiModelProperty(value = "排查责任单位")
    private String inspectUnit;

    /** 风险点描述 */
    @Excel(name = "风险点描述")
    @ApiModelProperty(value = "风险点描述")
    private String remakes;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "创建人ID")
    private Integer delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProjectId(Long projectId)
    {
        this.projectId = projectId;
    }

    public Long getProjectId()
    {
        return projectId;
    }

    public void setCity(String city)
    {
        this.city = city;
    }

    public String getCity()
    {
        return city;
    }

    public void setDistrict(String district) 
    {
        this.district = district;
    }

    public String getDistrict() 
    {
        return district;
    }

    public void setLot(String lot) 
    {
        this.lot = lot;
    }

    public String getLot() 
    {
        return lot;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setUnits(String units) 
    {
        this.units = units;
    }

    public String getUnits() 
    {
        return units;
    }

    public void setInspectType(String inspectType) 
    {
        this.inspectType = inspectType;
    }

    public String getInspectType() 
    {
        return inspectType;
    }

    public void setRoadNum(String roadNum) 
    {
        this.roadNum = roadNum;
    }

    public String getRoadNum() 
    {
        return roadNum;
    }

    public void setRiskLevel(Integer riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public Integer getRiskLevel() 
    {
        return riskLevel;
    }

    public void setIsPitfalls(Integer isPitfalls) 
    {
        this.isPitfalls = isPitfalls;
    }

    public Integer getIsPitfalls() 
    {
        return isPitfalls;
    }

    public void setPileStart(String pileStart) 
    {
        this.pileStart = pileStart;
    }

    public String getPileStart() 
    {
        return pileStart;
    }

    public void setPileEnd(String pileEnd) 
    {
        this.pileEnd = pileEnd;
    }

    public String getPileEnd() 
    {
        return pileEnd;
    }

    public void setIsMeasure(String isMeasure) 
    {
        this.isMeasure = isMeasure;
    }

    public String getIsMeasure() 
    {
        return isMeasure;
    }

    public void setMeasure(String measure)
    {
        this.measure = measure;
    }

    public String getMeasure()
    {
        return measure;
    }

    public void setSceneImg(String sceneImg) 
    {
        this.sceneImg = sceneImg;
    }

    public String getSceneImg() 
    {
        return sceneImg;
    }

    public void setMeasureFiles(String measureFiles) 
    {
        this.measureFiles = measureFiles;
    }

    public String getMeasureFiles() 
    {
        return measureFiles;
    }

    public void setProvinceUnit(String provinceUnit) 
    {
        this.provinceUnit = provinceUnit;
    }

    public String getProvinceUnit() 
    {
        return provinceUnit;
    }

    public void setReviewUnit(String reviewUnit) 
    {
        this.reviewUnit = reviewUnit;
    }

    public String getReviewUnit() 
    {
        return reviewUnit;
    }

    public void setInspectUnit(String inspectUnit) 
    {
        this.inspectUnit = inspectUnit;
    }

    public String getInspectUnit() 
    {
        return inspectUnit;
    }

    public void setRemakes(String remakes) 
    {
        this.remakes = remakes;
    }

    public String getRemakes() 
    {
        return remakes;
    }

    public void setCreateById(Long createById) 
    {
        this.createById = createById;
    }

    public Long getCreateById() 
    {
        return createById;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("lot", getLot())
            .append("lat", getLat())
            .append("units", getUnits())
            .append("inspectType", getInspectType())
            .append("roadNum", getRoadNum())
            .append("riskLevel", getRiskLevel())
            .append("isPitfalls", getIsPitfalls())
            .append("pileStart", getPileStart())
            .append("pileEnd", getPileEnd())
            .append("isMeasure", getIsMeasure())
            .append("measure", getMeasure())
            .append("sceneImg", getSceneImg())
            .append("measureFiles", getMeasureFiles())
            .append("provinceUnit", getProvinceUnit())
            .append("reviewUnit", getReviewUnit())
            .append("inspectUnit", getInspectUnit())
            .append("remakes", getRemakes())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("createById", getCreateById())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
