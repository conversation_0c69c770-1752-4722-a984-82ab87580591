package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.FillFields;
import com.tocc.risk.service.IFillFieldsService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 填报项字段Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags = "填报项字段")
@RestController
@RequestMapping("/risk/fields")
public class FillFieldsController extends BaseController
{
    @Autowired
    private IFillFieldsService fillFieldsService;

    /**
     * 查询填报项字段列表
     */
    @ApiOperation("获取填报项列表")
//    @PreAuthorize("@ss.hasPermi('system:fields:list')")
    @GetMapping("/list")
    public TableDataInfo list(FillFields fillFields)
    {
        startPage();
        List<FillFields> list = fillFieldsService.selectFillFieldsList(fillFields);

        return getDataTable(list);
    }

    /**
     * 导出填报项字段列表
     */
    @ApiOperation("导出填报项列表")
//    @PreAuthorize("@ss.hasPermi('system:fields:export')")
    @Log(title = "填报项字段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FillFields fillFields)
    {
        List<FillFields> list = fillFieldsService.selectFillFieldsList(fillFields);
        ExcelUtil<FillFields> util = new ExcelUtil<FillFields>(FillFields.class);
        util.exportExcel(response, list, "填报项字段数据");
    }

    /**
     * 获取填报项字段详细信息
     */
    @ApiOperation("获取填报项详情")
//    @PreAuthorize("@ss.hasPermi('system:fields:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(fillFieldsService.selectFillFieldsById(id));
    }

    /**
     * 新增填报项字段
     */
    @ApiOperation("新增填报项")
//    @PreAuthorize("@ss.hasPermi('system:fields:add')")
    @Log(title = "填报项字段", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FillFields fillFields)
    {
        return toAjax(fillFieldsService.insertFillFields(fillFields));
    }

    /**
     * 修改填报项字段
     */
    @ApiOperation("修改填报项")
//    @PreAuthorize("@ss.hasPermi('system:fields:edit')")
    @Log(title = "填报项字段", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody FillFields fillFields)
    {
        return toAjax(fillFieldsService.updateFillFields(fillFields));
    }

    /**
     * 删除填报项字段
     */
    @ApiOperation("删除填报项")
//    @PreAuthorize("@ss.hasPermi('system:fields:remove')")
    @Log(title = "填报项字段", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody FillFields fields)
    {
        return toAjax(fillFieldsService.deleteFillFieldsById(fields.getId()));
    }
}
