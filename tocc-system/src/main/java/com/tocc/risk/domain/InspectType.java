package com.tocc.risk.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 检查类别对象 risk_inspect_type
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@TableName("risk_inspect_type")
@ApiModel(value = "检查类别对象")
public class InspectType
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 类别名称 */
    @Excel(name = "类别名称")
    @ApiModelProperty(value = "类别名称")
    private String name;

    /** 层级 */
    @Excel(name = "层级")
    @ApiModelProperty(value = "层级")
    private Integer level;

    /** 上级ID */
    @Excel(name = "上级ID")
    @ApiModelProperty(value = "上级ID")
    private Long parentId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updataTime;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "更新时间")
    private Integer delFlag;

    @TableField(exist = false)
    @ApiModelProperty(value = "子级")
    private List<InspectType> children = new ArrayList<>();

    // 添加子节点
    public void addChild(InspectType child) {
        children.add(child);
    }

}
