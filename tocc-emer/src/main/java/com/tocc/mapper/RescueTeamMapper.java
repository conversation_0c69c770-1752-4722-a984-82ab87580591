package com.tocc.mapper;

import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.vo.RescueTeamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 救援队伍Mapper接口
 * 
 * <AUTHOR>
 */
public interface RescueTeamMapper {
    
    /**
     * 查询救援队伍
     * 
     * @param id 救援队伍主键
     * @return 救援队伍
     */
    RescueTeamVO selectRescueTeamById(String id);

    /**
     * 查询救援队伍列表
     * 
     * @param rescueTeam 救援队伍
     * @return 救援队伍集合
     */
    List<RescueTeamVO> selectRescueTeamList(RescueTeamDTO rescueTeam);

    /**
     * 新增救援队伍
     * 
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    int insertRescueTeam(RescueTeamDTO rescueTeam);

    /**
     * 修改救援队伍
     * 
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    int updateRescueTeam(RescueTeamDTO rescueTeam);

    /**
     * 删除救援队伍
     * 
     * @param id 救援队伍主键
     * @return 结果
     */
    int deleteRescueTeamById(String id);

    /**
     * 批量删除救援队伍
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRescueTeamByIds(String[] ids);

    /**
     * 检查队伍编号是否唯一
     * 
     * @param teamCode 队伍编号
     * @param id 队伍ID（修改时排除自己）
     * @return 结果
     */
    int checkTeamCodeUnique(@Param("teamCode") String teamCode, @Param("id") String id);
}
