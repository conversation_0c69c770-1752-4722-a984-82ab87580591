package com.tocc.risk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 检查审批对象 risk_approve
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "检查审批对象")
public class Approve extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 隐患ID */
    @Excel(name = "隐患ID")
    @ApiModelProperty(value = "隐患ID")
    private Long pitfallsId;

    /** 任务下发ID */
    @Excel(name = "任务下发ID")
    @ApiModelProperty(value = "任务下发ID")
    private Long issuedId;

    /** 任务填报ID */
    @Excel(name = "任务填报ID")
    @ApiModelProperty(value = "任务填报ID")
    private Long taskId;

    /** 状态（0-待填报，1-通过，2-驳回） */
    @Excel(name = "状态", readConverterExp = "0=-待填报，1-通过，2-驳回")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 复核审核人 */
    @Excel(name = "复核审核人")
    @ApiModelProperty(value = "复核审核人")
    private String approveBy;

    /** 复核审核人ID */
    @Excel(name = "复核审核人ID")
    @ApiModelProperty(value = "复核审核人ID")
    private Long approveById;

    /** 复核审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "复核审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "复核审核时间")
    private Date approveTime;

    /** 省级审核人 */
    @Excel(name = "省级审核人")
    @ApiModelProperty(value = "省级审核人")
    private String provinceBy;

    /** 省级审核人ID */
    @Excel(name = "省级审核人ID")
    @ApiModelProperty(value = "省级审核人ID")
    private Long provinceById;

    /** 省级审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "省级审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "省级审核时间")
    private Date provinceTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPitfallsId(Long pitfallsId) 
    {
        this.pitfallsId = pitfallsId;
    }

    public Long getPitfallsId() 
    {
        return pitfallsId;
    }

    public void setIssuedId(Long issuedId) 
    {
        this.issuedId = issuedId;
    }

    public Long getIssuedId() 
    {
        return issuedId;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setApproveBy(String approveBy) 
    {
        this.approveBy = approveBy;
    }

    public String getApproveBy() 
    {
        return approveBy;
    }

    public void setApproveById(Long approveById) 
    {
        this.approveById = approveById;
    }

    public Long getApproveById() 
    {
        return approveById;
    }

    public void setApproveTime(Date approveTime) 
    {
        this.approveTime = approveTime;
    }

    public Date getApproveTime() 
    {
        return approveTime;
    }

    public void setProvinceBy(String provinceBy) 
    {
        this.provinceBy = provinceBy;
    }

    public String getProvinceBy() 
    {
        return provinceBy;
    }

    public void setProvinceById(Long provinceById) 
    {
        this.provinceById = provinceById;
    }

    public Long getProvinceById() 
    {
        return provinceById;
    }

    public void setProvinceTime(Date provinceTime) 
    {
        this.provinceTime = provinceTime;
    }

    public Date getProvinceTime() 
    {
        return provinceTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pitfallsId", getPitfallsId())
            .append("issuedId", getIssuedId())
            .append("taskId", getTaskId())
            .append("status", getStatus())
            .append("approveBy", getApproveBy())
            .append("approveById", getApproveById())
            .append("approveTime", getApproveTime())
            .append("provinceBy", getProvinceBy())
            .append("provinceById", getProvinceById())
            .append("provinceTime", getProvinceTime())
            .toString();
    }
}
