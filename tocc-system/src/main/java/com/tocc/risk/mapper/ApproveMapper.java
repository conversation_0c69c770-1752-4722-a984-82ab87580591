package com.tocc.risk.mapper;

import java.util.List;
import com.tocc.risk.domain.Approve;

/**
 * 检查审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface ApproveMapper 
{
    /**
     * 查询检查审批
     * 
     * @param id 检查审批主键
     * @return 检查审批
     */
    public Approve selectApproveById(Long id);

    /**
     * 查询检查审批列表
     * 
     * @param approve 检查审批
     * @return 检查审批集合
     */
    public List<Approve> selectApproveList(Approve approve);

    /**
     * 新增检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    public int insertApprove(Approve approve);

    /**
     * 修改检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    public int updateApprove(Approve approve);

    /**
     * 删除检查审批
     * 
     * @param id 检查审批主键
     * @return 结果
     */
    public int deleteApproveById(Long id);

    /**
     * 批量删除检查审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApproveByIds(Long[] ids);
}
