package com.tocc.risk.service.impl;

import java.util.List;

import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.InspectTaskMapper;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.service.IInspectTaskService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检查任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class InspectTaskServiceImpl implements IInspectTaskService 
{
    @Autowired
    private InspectTaskMapper inspectTaskMapper;
    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询检查任务
     * 
     * @param id 检查任务主键
     * @return 检查任务
     */
    @Override
    public InspectTask selectInspectTaskById(Long id)
    {
        return inspectTaskMapper.selectInspectTaskById(id);
    }

    /**
     * 查询检查任务列表
     * 
     * @param inspectTask 检查任务
     * @return 检查任务
     */
    @Override
    public List<InspectIssued> selectInspectTaskList(InspectTask inspectTask)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        inspectTask.setInformantId(loginUser.getUserId());
        return inspectTaskMapper.selectInspectTaskList(inspectTask);
    }

    /**
     * 新增检查任务
     * 
     * @param inspectTask 检查任务
     * @return 结果
     */
    @Override
    @Transactional
    public int insertInspectTask(InspectTask inspectTask)
    {
        // 通过人员ID分别下发任务
        String[] ids = inspectTask.getInformant().split(",");
        for (String id : ids) {
            SysUser user = userMapper.selectUserById(Long.parseLong(id));
            InspectTask task = new InspectTask();
            task.setInformantId(user.getUserId());
            task.setInformant(user.getNickName());
            task.setIssuedId(inspectTask.getIssuedId());
            task.setStatus(0);
            task.setCreateTime(DateUtils.getNowDate());
            task.setDelFlag(0);
            inspectTaskMapper.insertInspectTask(task);
        }
        return 1;
    }

    /**
     * 修改检查任务
     * 
     * @param inspectTask 检查任务
     * @return 结果
     */
    @Override
    public int updateInspectTask(InspectTask inspectTask)
    {
        inspectTask.setUpdateTime(DateUtils.getNowDate());
        return inspectTaskMapper.updateInspectTask(inspectTask);
    }

    /**
     * 批量删除检查任务
     * 
     * @param ids 需要删除的检查任务主键
     * @return 结果
     */
    @Override
    public int deleteInspectTaskByIds(Long[] ids)
    {
        return inspectTaskMapper.deleteInspectTaskByIds(ids);
    }

    /**
     * 删除检查任务信息
     * 
     * @param id 检查任务主键
     * @return 结果
     */
    @Override
    public int deleteInspectTaskById(Long id)
    {
        return inspectTaskMapper.deleteInspectTaskById(id);
    }
}
