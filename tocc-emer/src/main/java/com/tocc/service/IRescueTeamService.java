package com.tocc.service;

import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.MaterialVO;

import java.util.List;

/**
 * 救援队伍Service接口
 * 
 * <AUTHOR>
 */
public interface IRescueTeamService {
    
    /**
     * 查询救援队伍
     * 
     * @param id 救援队伍主键
     * @return 救援队伍
     */
    RescueTeamVO selectRescueTeamById(String id);

    /**
     * 查询救援队伍列表
     * 
     * @param rescueTeam 救援队伍
     * @return 救援队伍集合
     */
    List<RescueTeamVO> selectRescueTeamList(RescueTeamDTO rescueTeam);

    /**
     * 新增救援队伍
     * 
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    int insertRescueTeam(RescueTeamDTO rescueTeam);

    /**
     * 修改救援队伍
     * 
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    int updateRescueTeam(RescueTeamDTO rescueTeam);

    /**
     * 批量删除救援队伍
     * 
     * @param ids 需要删除的救援队伍主键集合
     * @return 结果
     */
    int deleteRescueTeamByIds(String[] ids);

    /**
     * 删除救援队伍信息
     * 
     * @param id 救援队伍主键
     * @return 结果
     */
    int deleteRescueTeamById(String id);

    /**
     * 检查队伍编号是否唯一
     * 
     * @param teamCode 队伍编号
     * @param id 队伍ID（修改时排除自己）
     * @return 结果
     */
    boolean checkTeamCodeUnique(String teamCode, String id);

    /**
     * 查询救援队伍的物资列表
     * 
     * @param teamId 队伍ID
     * @return 物资列表
     */
    List<MaterialVO> selectTeamMaterials(String teamId);

    /**
     * 查询救援队伍的装备列表
     * 
     * @param teamId 队伍ID
     * @return 装备列表
     */
    List<MaterialVO> selectTeamEquipments(String teamId);

    /**
     * 更新救援队伍的物资配置
     * 
     * @param teamId 队伍ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    int updateTeamMaterials(String teamId, List<String> materialIds);

    /**
     * 添加救援队伍物资
     * 
     * @param teamId 队伍ID
     * @param materialId 物资ID
     * @return 结果
     */
    int addTeamMaterial(String teamId, String materialId);

    /**
     * 移除救援队伍物资
     * 
     * @param teamId 队伍ID
     * @param materialId 物资ID
     * @return 结果
     */
    int removeTeamMaterial(String teamId, String materialId);
}
