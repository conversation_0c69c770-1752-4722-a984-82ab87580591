package com.tocc.system.service;

import java.util.List;
import com.tocc.system.domain.SysRoadMarker;
import com.tocc.system.domain.vo.RoadMarkerTreeVO;

/**
 * 公路编号Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface ISysRoadMarkerService 
{
    /**
     * 查询公路编号
     * 
     * @param id 公路编号主键
     * @return 公路编号
     */
    public SysRoadMarker selectSysRoadMarkerById(Long id);

    /**
     * 查询公路编号列表
     * 
     * @param sysRoadMarker 公路编号
     * @return 公路编号集合
     */
    public List<SysRoadMarker> selectSysRoadMarkerList(SysRoadMarker sysRoadMarker);

    /**
     * 新增公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    public int insertSysRoadMarker(SysRoadMarker sysRoadMarker);

    /**
     * 修改公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    public int updateSysRoadMarker(SysRoadMarker sysRoadMarker);

    /**
     * 批量删除公路编号
     * 
     * @param ids 需要删除的公路编号主键集合
     * @return 结果
     */
    public int deleteSysRoadMarkerByIds(Long[] ids);

    /**
     * 删除公路编号信息
     *
     * @param id 公路编号主键
     * @return 结果
     */
    public int deleteSysRoadMarkerById(Long id);

    /**
     * 获取公路编号树形结构
     *
     * @return 树形结构数据
     */
    public List<RoadMarkerTreeVO> getRoadMarkerTree();
}
