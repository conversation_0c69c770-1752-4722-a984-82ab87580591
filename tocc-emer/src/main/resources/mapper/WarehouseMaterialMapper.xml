<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.WarehouseMaterialMapper">
    
    <resultMap type="com.tocc.domain.vo.MaterialVO" id="MaterialResult">
        <result property="id"                column="id"                />
        <result property="materialName"      column="material_name"     />
        <result property="materialType"      column="material_type"     />
        <result property="materialTypeName"  column="material_type_name" />
        <result property="specModel"         column="spec_model"        />
        <result property="categoryCode"      column="category_code"     />
        <result property="warehouseId"       column="warehouse_id"      />
        <result property="quantity"          column="quantity"          />
        <result property="unit"              column="unit"              />
        <result property="status"            column="status"            />
        <result property="statusName"        column="status_name"       />
        <result property="expiryDate"        column="expiry_date"       />
        <result property="remark"            column="remark"            />
        <result property="teamId"            column="team_id"           />
        <result property="createTime"        column="create_time"       />
        <result property="creator"           column="creator"           />
        <result property="updateTime"        column="update_time"       />
        <result property="updater"           column="updater"           />
    </resultMap>

    <sql id="selectMaterialVo">
        select 
            em.id, em.material_name, em.material_type, em.spec_model, em.category_code,
            em.warehouse_id, em.quantity, em.unit, em.status, em.expiry_date, em.remark,
            em.team_id, em.create_time, em.creator, em.update_time, em.updater,
            -- 字典值转换
            case em.material_type 
                when '0' then '应急物资'
                when '1' then '应急装备'
                else '未知'
            end as material_type_name,
            case em.status 
                when 1 then '正常'
                when 2 then '待检修'
                when 3 then '报废'
                else '未知'
            end as status_name
        from em_material em
        inner join em_warehouse_material wm on em.id = wm.material_id
    </sql>

    <select id="selectMaterialsByWarehouseId" parameterType="String" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where wm.warehouse_id = #{warehouseId} and em.del_flag = 0
        order by em.material_type, em.material_name
    </select>

    <select id="selectMaterialsByWarehouseIdAndType" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where wm.warehouse_id = #{warehouseId} and em.material_type = #{materialType} and em.del_flag = 0
        order by em.material_name
    </select>

    <insert id="insertWarehouseMaterial" parameterType="com.tocc.domain.dto.WarehouseMaterialDTO">
        insert into em_warehouse_material (warehouse_id, material_id)
        values (#{warehouseId}, #{materialId})
    </insert>

    <insert id="batchInsertWarehouseMaterials" parameterType="java.util.List">
        insert into em_warehouse_material (warehouse_id, material_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseId}, #{item.materialId})
        </foreach>
    </insert>

    <delete id="deleteWarehouseMaterialsByWarehouseId" parameterType="String">
        delete from em_warehouse_material where warehouse_id = #{warehouseId}
    </delete>

    <delete id="deleteWarehouseMaterial">
        delete from em_warehouse_material 
        where warehouse_id = #{warehouseId} and material_id = #{materialId}
    </delete>

</mapper>
