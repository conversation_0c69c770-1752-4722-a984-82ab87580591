<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.AlarmInfoMapper">
    
    <resultMap type="com.tocc.domain.vo.AlarmInfoVO" id="AlarmInfoResult">
        <result property="alarmId"       column="alarm_id"       />
        <result property="alarmTitle"    column="alarm_title"    />
        <result property="alarmType"     column="alarm_type"     />
        <result property="alarmTypeName" column="alarm_type_name" />
        <result property="alarmSubtype"  column="alarm_subtype"  />
        <result property="alarmSubtypeName" column="alarm_subtype_name" />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="alarmLevelName" column="alarm_level_name" />
        <result property="alarmContent"  column="alarm_content"  />
        <result property="sourceId"      column="source_id"      />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceTypeName" column="source_type_name" />
        <result property="orgId"         column="org_id"         />
        <result property="orgName"       column="org_name"       />
        <result property="status"        column="status"         />
        <result property="statusName"    column="status_name"    />
        <result property="alarmTime"     column="alarm_time"     />
        <result property="processTime"   column="process_time"   />
        <result property="processorId"   column="processor_id"   />
        <result property="processorName" column="processor_name" />
        <result property="processResult" column="process_result" />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <result property="administrativeAreaId" column="administrative_area_id" />
        <result property="administrativeArea" column="administrative_area" />
    </resultMap>

    <sql id="selectAlarmInfoVo">
        select 
            a.alarm_id, a.alarm_title, a.alarm_type, a.alarm_subtype, a.alarm_level,
            a.alarm_content, a.source_id, a.source_type, a.org_id, a.org_name,
            a.status, a.alarm_time, a.process_time, a.processor_id, a.processor_name,
            a.process_result, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
            a.administrative_area_id, a.administrative_area,
            -- 字典值转换
            dt1.dict_label as alarm_type_name,
            dt2.dict_label as alarm_subtype_name,
            dt3.dict_label as alarm_level_name,
            dt4.dict_label as source_type_name,
            case a.status 
                when '0' then '未处理'
                when '1' then '已处理'
                when '2' then '已忽略'
                else '未知'
            end as status_name
        from alarm_info a
        left join sys_dict_data dt1 on dt1.dict_value = a.alarm_type and dt1.dict_type = 'alarm_type'
        left join sys_dict_data dt2 on dt2.dict_value = a.alarm_subtype and dt2.dict_type = 'alarm_subtype'
        left join sys_dict_data dt3 on dt3.dict_value = a.alarm_level and dt3.dict_type = 'alarm_level'
        left join sys_dict_data dt4 on dt4.dict_value = a.source_type and dt4.dict_type = 'source_type'
    </sql>

    <select id="selectAlarmInfoList" parameterType="com.tocc.domain.dto.AlarmInfoDTO" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        <where>  
            <if test="alarmTitle != null and alarmTitle != ''"> and a.alarm_title like concat('%', #{alarmTitle}, '%')</if>
            <if test="alarmType != null and alarmType != ''"> and a.alarm_type = #{alarmType}</if>
            <if test="alarmSubtype != null and alarmSubtype != ''"> and a.alarm_subtype = #{alarmSubtype}</if>
            <if test="alarmLevel != null and alarmLevel != ''"> and a.alarm_level = #{alarmLevel}</if>
            <if test="sourceType != null and sourceType != ''"> and a.source_type = #{sourceType}</if>
            <if test="orgId != null and orgId != ''"> and a.org_id = #{orgId}</if>
            <if test="status != null and status != ''"> and a.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(a.alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(a.alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by a.alarm_time desc
    </select>
    
    <select id="selectAlarmInfoByAlarmId" parameterType="String" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        where a.alarm_id = #{alarmId}
    </select>

    <select id="selectAlarmInfoByOrgIds" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        <where>
            <if test="orgIds != null and orgIds.size() > 0">
                and a.org_id in
                <foreach item="orgId" collection="orgIds" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="alarmInfo.alarmTitle != null and alarmInfo.alarmTitle != ''"> and a.alarm_title like concat('%', #{alarmInfo.alarmTitle}, '%')</if>
            <if test="alarmInfo.alarmType != null and alarmInfo.alarmType != ''"> and a.alarm_type = #{alarmInfo.alarmType}</if>
            <if test="alarmInfo.alarmSubtype != null and alarmInfo.alarmSubtype != ''"> and a.alarm_subtype = #{alarmInfo.alarmSubtype}</if>
            <if test="alarmInfo.alarmLevel != null and alarmInfo.alarmLevel != ''"> and a.alarm_level = #{alarmInfo.alarmLevel}</if>
            <if test="alarmInfo.sourceType != null and alarmInfo.sourceType != ''"> and a.source_type = #{alarmInfo.sourceType}</if>
            <if test="alarmInfo.status != null and alarmInfo.status != ''"> and a.status = #{alarmInfo.status}</if>
        </where>
        order by a.alarm_time desc
    </select>

    <select id="countAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO" resultType="int">
        select count(*) from alarm_info a
        <where>  
            <if test="alarmType != null and alarmType != ''"> and a.alarm_type = #{alarmType}</if>
            <if test="alarmSubtype != null and alarmSubtype != ''"> and a.alarm_subtype = #{alarmSubtype}</if>
            <if test="alarmLevel != null and alarmLevel != ''"> and a.alarm_level = #{alarmLevel}</if>
            <if test="sourceType != null and sourceType != ''"> and a.source_type = #{sourceType}</if>
            <if test="orgId != null and orgId != ''"> and a.org_id = #{orgId}</if>
            <if test="status != null and status != ''"> and a.status = #{status}</if>
        </where>
    </select>
        
    <insert id="insertAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO">
        insert into alarm_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmId != null">alarm_id,</if>
            <if test="alarmTitle != null">alarm_title,</if>
            <if test="alarmType != null">alarm_type,</if>
            <if test="alarmSubtype != null">alarm_subtype,</if>
            <if test="alarmLevel != null">alarm_level,</if>
            <if test="alarmContent != null">alarm_content,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="status != null">status,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="processTime != null">process_time,</if>
            <if test="processorId != null">processor_id,</if>
            <if test="processorName != null">processor_name,</if>
            <if test="processResult != null">process_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="administrativeArea != null">administrative_area,</if>
            <if test="administrativeAreaId != null">administrative_area_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmId != null">#{alarmId},</if>
            <if test="alarmTitle != null">#{alarmTitle},</if>
            <if test="alarmType != null">#{alarmType},</if>
            <if test="alarmSubtype != null">#{alarmSubtype},</if>
            <if test="alarmLevel != null">#{alarmLevel},</if>
            <if test="alarmContent != null">#{alarmContent},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="status != null">#{status},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="processorId != null">#{processorId},</if>
            <if test="processorName != null">#{processorName},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="administrativeArea != null">#{administrativeArea},</if>
            <if test="administrativeAreaId != null">#{administrativeAreaId},</if>
         </trim>
    </insert>

    <update id="updateAlarmInfo" parameterType="com.tocc.domain.dto.AlarmInfoDTO">
        update alarm_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmTitle != null">alarm_title = #{alarmTitle},</if>
            <if test="alarmType != null">alarm_type = #{alarmType},</if>
            <if test="alarmSubtype != null">alarm_subtype = #{alarmSubtype},</if>
            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>
            <if test="alarmContent != null">alarm_content = #{alarmContent},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="processorId != null">processor_id = #{processorId},</if>
            <if test="processorName != null">processor_name = #{processorName},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="administrativeArea != null">administrative_area = #{administrativeArea},</if>
            <if test="administrativeAreaId != null">administrative_area_id = #{administrativeAreaId},</if>
        </trim>
        where alarm_id = #{alarmId}
    </update>

    <update id="updateAlarmStatus">
        update alarm_info 
        set status = #{status},
            process_time = now(),
            processor_id = #{processorId},
            processor_name = #{processorName},
            process_result = #{processResult},
            update_by = #{updateBy},
            update_time = now()
        where alarm_id = #{alarmId}
    </update>

    <delete id="deleteAlarmInfoByAlarmId" parameterType="String">
        delete from alarm_info where alarm_id = #{alarmId}
    </delete>

    <delete id="deleteAlarmInfoByAlarmIds" parameterType="String">
        delete from alarm_info where alarm_id in 
        <foreach item="alarmId" collection="array" open="(" separator="," close=")">
            #{alarmId}
        </foreach>
    </delete>
</mapper>
