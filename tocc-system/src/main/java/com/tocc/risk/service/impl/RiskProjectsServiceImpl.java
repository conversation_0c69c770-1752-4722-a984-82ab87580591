package com.tocc.risk.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.RiskProjectsMapper;
import com.tocc.risk.domain.Projects;
import com.tocc.risk.service.IRiskProjectsService;

/**
 * 项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class RiskProjectsServiceImpl implements IRiskProjectsService 
{
    @Autowired
    private RiskProjectsMapper riskProjectsMapper;

    /**
     * 查询项目
     * 
     * @param id 项目主键
     * @return 项目
     */
    @Override
    public Projects selectRiskProjectsById(Long id)
    {
        return riskProjectsMapper.selectRiskProjectsById(id);
    }

    /**
     * 查询项目列表
     * 
     * @param projects 项目
     * @return 项目
     */
    @Override
    public List<Projects> selectRiskProjectsList(Projects projects)
    {
        return riskProjectsMapper.selectRiskProjectsList(projects);
    }

    /**
     * 新增项目
     * 
     * @param projects 项目
     * @return 结果
     */
    @Override
    public int insertRiskProjects(Projects projects)
    {
        projects.setCreateTime(DateUtils.getNowDate());
        return riskProjectsMapper.insertRiskProjects(projects);
    }

    /**
     * 修改项目
     * 
     * @param projects 项目
     * @return 结果
     */
    @Override
    public int updateRiskProjects(Projects projects)
    {
        projects.setUpdateTime(DateUtils.getNowDate());
        return riskProjectsMapper.updateRiskProjects(projects);
    }

    /**
     * 批量删除项目
     * 
     * @param ids 需要删除的项目主键
     * @return 结果
     */
    @Override
    public int deleteRiskProjectsByIds(Long[] ids)
    {
        return riskProjectsMapper.deleteRiskProjectsByIds(ids);
    }

    /**
     * 删除项目信息
     * 
     * @param id 项目主键
     * @return 结果
     */
    @Override
    public int deleteRiskProjectsById(Long id)
    {
        return riskProjectsMapper.deleteRiskProjectsById(id);
    }
}
