package com.tocc.service.impl;

import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.dto.WarehouseMaterialDTO;
import com.tocc.domain.dto.MaterialDTO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.mapper.WarehouseMapper;
import com.tocc.mapper.WarehouseMaterialMapper;
import com.tocc.mapper.MaterialMapper;
import com.tocc.service.IWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 仓库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WarehouseServiceImpl implements IWarehouseService {
    
    @Autowired
    private WarehouseMapper warehouseMapper;
    
    @Autowired
    private WarehouseMaterialMapper warehouseMaterialMapper;
    
    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 查询仓库
     * 
     * @param id 仓库主键
     * @return 仓库
     */
    @Override
    public WarehouseVO selectWarehouseById(String id) {
        WarehouseVO warehouse = warehouseMapper.selectWarehouseById(id);
        if (warehouse != null) {
            // 查询关联的物资
            List<MaterialVO> materials = warehouseMaterialMapper.selectMaterialsByWarehouseId(id);
            warehouse.setMaterials(materials);
        }
        return warehouse;
    }

    /**
     * 查询仓库列表
     * 
     * @param warehouse 仓库
     * @return 仓库
     */
    @Override
    public List<WarehouseVO> selectWarehouseList(WarehouseDTO warehouse) {
        List<WarehouseVO> warehouseList = warehouseMapper.selectWarehouseList(warehouse);
        
        // 为每个仓库加载关联的物资
        for (WarehouseVO warehouseVO : warehouseList) {
            List<MaterialVO> materials = warehouseMaterialMapper.selectMaterialsByWarehouseId(warehouseVO.getId());
            warehouseVO.setMaterials(materials);
        }
        
        return warehouseList;
    }

    /**
     * 新增仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWarehouse(WarehouseDTO warehouse) {
        // 生成仓库ID
        String warehouseId = IdUtils.simpleUUID();
        warehouse.setId(warehouseId);
        warehouse.setCreator(getUsername());
        warehouse.setDelFlag(0);
        
        // 插入仓库基础信息
        int result = warehouseMapper.insertWarehouse(warehouse);
        
        if (result > 0) {
            // 创建仓库专属的物资
            if (warehouse.getMaterials() != null && !warehouse.getMaterials().isEmpty()) {
                createWarehouseMaterials(warehouseId, warehouse.getMaterials());
            }
        }
        
        return result;
    }

    /**
     * 修改仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarehouse(WarehouseDTO warehouse) {
        warehouse.setUpdater(getUsername());
        
        // 更新仓库基础信息
        int result = warehouseMapper.updateWarehouse(warehouse);
        
        // 注意：修改时不处理物资，物资的管理通过专门的接口进行
        
        return result;
    }

    /**
     * 批量删除仓库
     * 
     * @param ids 需要删除的仓库主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWarehouseByIds(String[] ids) {
        // 删除物资关联关系
        for (String id : ids) {
            warehouseMaterialMapper.deleteWarehouseMaterialsByWarehouseId(id);
        }
        
        // 软删除仓库
        return warehouseMapper.deleteWarehouseByIds(ids);
    }

    /**
     * 删除仓库信息
     * 
     * @param id 仓库主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWarehouseById(String id) {
        // 删除物资关联关系
        warehouseMaterialMapper.deleteWarehouseMaterialsByWarehouseId(id);
        
        // 软删除仓库
        return warehouseMapper.deleteWarehouseById(id);
    }

    /**
     * 查询仓库的物资列表
     * 
     * @param warehouseId 仓库ID
     * @return 物资列表
     */
    @Override
    public List<MaterialVO> selectWarehouseMaterials(String warehouseId) {
        return warehouseMaterialMapper.selectMaterialsByWarehouseId(warehouseId);
    }

    /**
     * 更新仓库的物资配置
     * 
     * @param warehouseId 仓库ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarehouseMaterials(String warehouseId, List<String> materialIds) {
        // 先删除原有关联
        warehouseMaterialMapper.deleteWarehouseMaterialsByWarehouseId(warehouseId);
        
        // 插入新的关联
        if (materialIds != null && !materialIds.isEmpty()) {
            return insertWarehouseMaterials(warehouseId, materialIds);
        }
        
        return 1;
    }

    /**
     * 添加仓库物资
     * 
     * @param warehouseId 仓库ID
     * @param materialId 物资ID
     * @return 结果
     */
    @Override
    public int addWarehouseMaterial(String warehouseId, String materialId) {
        WarehouseMaterialDTO warehouseMaterial = new WarehouseMaterialDTO();
        warehouseMaterial.setWarehouseId(warehouseId);
        warehouseMaterial.setMaterialId(materialId);
        return warehouseMaterialMapper.insertWarehouseMaterial(warehouseMaterial);
    }

    /**
     * 移除仓库物资
     * 
     * @param warehouseId 仓库ID
     * @param materialId 物资ID
     * @return 结果
     */
    @Override
    public int removeWarehouseMaterial(String warehouseId, String materialId) {
        return warehouseMaterialMapper.deleteWarehouseMaterial(warehouseId, materialId);
    }

    /**
     * 创建仓库专属物资
     * 
     * @param warehouseId 仓库ID
     * @param materials 物资列表
     */
    private void createWarehouseMaterials(String warehouseId, List<MaterialDTO> materials) {
        for (MaterialDTO material : materials) {
            // 创建物资记录
            String materialId = IdUtils.simpleUUID();
            material.setId(materialId);
            material.setWarehouseId(warehouseId); // 设置所属仓库
            material.setStatus(1); // 默认正常状态
            material.setCreator(getUsername());
            material.setDelFlag(0);
            
            // 插入em_material表
            materialMapper.insertMaterial(material);
            
            // 创建关联关系
            WarehouseMaterialDTO warehouseMaterial = new WarehouseMaterialDTO();
            warehouseMaterial.setWarehouseId(warehouseId);
            warehouseMaterial.setMaterialId(materialId);
            warehouseMaterialMapper.insertWarehouseMaterial(warehouseMaterial);
        }
    }

    /**
     * 批量插入仓库物资关联（用于现有物资的关联）
     * 
     * @param warehouseId 仓库ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    private int insertWarehouseMaterials(String warehouseId, List<String> materialIds) {
        List<WarehouseMaterialDTO> warehouseMaterials = new ArrayList<>();
        for (String materialId : materialIds) {
            WarehouseMaterialDTO warehouseMaterial = new WarehouseMaterialDTO();
            warehouseMaterial.setWarehouseId(warehouseId);
            warehouseMaterial.setMaterialId(materialId);
            warehouseMaterials.add(warehouseMaterial);
        }
        return warehouseMaterialMapper.batchInsertWarehouseMaterials(warehouseMaterials);
    }

    /**
     * 获取当前用户名
     * 
     * @return 用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }
}
