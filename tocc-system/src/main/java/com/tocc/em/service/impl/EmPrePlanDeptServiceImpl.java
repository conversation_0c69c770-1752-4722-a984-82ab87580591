package com.tocc.em.service.impl;

import java.util.*;

import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.UUID;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.dto.EmPrePlanDeptDTO;
import com.tocc.em.dto.EmPrePlanDeptUserDTO;
import com.tocc.em.service.IEmPrePlanDeptUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanDeptMapper;
import com.tocc.em.domain.EmPrePlanDept;
import com.tocc.em.service.IEmPrePlanDeptService;

import javax.annotation.Resource;

/**
 * 应急预案组织体系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanDeptServiceImpl implements IEmPrePlanDeptService
{
    @Autowired
    private EmPrePlanDeptMapper emPrePlanDeptMapper;


    @Resource
    private IEmPrePlanDeptUserService iEmPrePlanDeptUserService ;
    /**
     * 查询应急预案组织体系
     *
     * @param id 应急预案组织体系主键
     * @return 应急预案组织体系
     */
    @Override
    public EmPrePlanDept selectEmPrePlanDeptById(String id)
    {
        return emPrePlanDeptMapper.selectEmPrePlanDeptById(id);
    }

    /**
     * 查询应急预案组织体系列表
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 应急预案组织体系
     */
    @Override
    public List<EmPrePlanDept> selectEmPrePlanDeptList(EmPrePlanDept emPrePlanDept)
    {
        return emPrePlanDeptMapper.selectEmPrePlanDeptList(emPrePlanDept);
    }

    /**
     * 新增应急预案组织体系
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    @Override
    public int insertEmPrePlanDept(EmPrePlanDept emPrePlanDept)
    {
        emPrePlanDept.setCreateTime(DateUtils.getNowDate());
        return emPrePlanDeptMapper.insertEmPrePlanDept(emPrePlanDept);
    }

    /**
     * 修改应急预案组织体系
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    @Override
    public int updateEmPrePlanDept(EmPrePlanDept emPrePlanDept)
    {
        emPrePlanDept.setUpdateTime(DateUtils.getNowDate());
        return emPrePlanDeptMapper.updateEmPrePlanDept(emPrePlanDept);
    }

    /**
     * 批量删除应急预案组织体系
     *
     * @param ids 需要删除的应急预案组织体系主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptByIds(String[] ids)
    {
        return emPrePlanDeptMapper.deleteEmPrePlanDeptByIds(ids);
    }
    @Override
    public List<EmPrePlanDeptDTO> selectEmPrePlanDeptListWithTree(EmPrePlanDept emPrePlanDept) {
        List<EmPrePlanDept> emPrePlanDepts = this.selectEmPrePlanDeptList(emPrePlanDept);
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList = new ArrayList<>(emPrePlanDepts.size());
        for (EmPrePlanDept prePlanDept : emPrePlanDepts) {
            EmPrePlanDeptDTO emPrePlanDeptDTO = new EmPrePlanDeptDTO();
            BeanUtils.copyProperties(prePlanDept, emPrePlanDeptDTO);
            //机构人员
            String id = prePlanDept.getId();
            EmPrePlanDeptUser emPrePlanDeptUser = new EmPrePlanDeptUser();
            emPrePlanDeptUser.setEmDeptId(id);
            List<EmPrePlanDeptUser> emPrePlanDeptUsers = iEmPrePlanDeptUserService.selectEmPrePlanDeptUserList(emPrePlanDeptUser);
            List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList = new ArrayList<>(emPrePlanDeptUsers.size());
            for (EmPrePlanDeptUser emPrePlanDeptUserEach:emPrePlanDeptUsers){
                EmPrePlanDeptUserDTO emPrePlanDeptUserDTO = new EmPrePlanDeptUserDTO();
                BeanUtils.copyProperties(emPrePlanDeptUserEach, emPrePlanDeptUserDTO);
                emPrePlanDeptUserDTOList.add(emPrePlanDeptUserDTO);
            }
            emPrePlanDeptDTO.setEmPrePlanDeptUserDTOList(emPrePlanDeptUserDTOList);
            emPrePlanDeptDTOList.add(emPrePlanDeptDTO);
        }
      return   buildTree(emPrePlanDeptDTOList);

    }

    /**
     * 将扁平列表转换为树形结构
     * @param list 包含所有节点的扁平列表
     * @return 完整的树形结构
     */
    public static List<EmPrePlanDeptDTO> buildTree(List<EmPrePlanDeptDTO> list) {
        // 创建结果集合和临时map
        List<EmPrePlanDeptDTO> result = new ArrayList<>();
        Map<String, EmPrePlanDeptDTO> nodeMap = new HashMap<>();

        // 第一次遍历：将所有节点存入map
        for (EmPrePlanDeptDTO node : list) {
            nodeMap.put(node.getId(), node);
            if (node.getChildren() == null) {
                node.setChildren(new ArrayList<>());
            }
        }

        // 第二次遍历：建立父子关系
        for (EmPrePlanDeptDTO node : list) {
            String parentId = node.getParentId();
            if (parentId == null || parentId.isEmpty()) {
                // 顶级节点
                result.add(node);
            } else {
                // 子节点处理
                EmPrePlanDeptDTO parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(node);
                    // 设置机构级别
                    node.setDeptLevel(1);
                }
            }
        }
        return result;
    }
    /**
     * 删除应急预案组织体系信息
     *
     * @param id 应急预案组织体系主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptById(String id)
    {
        return emPrePlanDeptMapper.deleteEmPrePlanDeptById(id);
    }

    @Override
    public void saveEmPrePlanDept(String version, String emPrePlanID, List<EmPrePlanDeptDTO> emPrePlanDeptDTOList) {
        String loginUserName = SecurityUtils.getLoginUser().getUser().getUserName();
        //构建树形
        generateIds(emPrePlanDeptDTOList,emPrePlanID);
        List<EmPrePlanDeptDTO> flatList = flattenTree(emPrePlanDeptDTOList);
        List<EmPrePlanDept> flatListFinal = new ArrayList<>();
        // 2. 批量保存
        flatList.forEach(dept -> {

            EmPrePlanDept  emPrePlanDept = new EmPrePlanDept();
            BeanUtils.copyProperties(dept,emPrePlanDept);
            emPrePlanDept.setCreator(loginUserName);
            emPrePlanDept.setUpdater(loginUserName);
            emPrePlanDept.setVersion(version);
            flatListFinal.add(emPrePlanDept);
            //机构人员
            List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList = dept.getEmPrePlanDeptUserDTOList();
            if(null != emPrePlanDeptUserDTOList && !emPrePlanDeptUserDTOList.isEmpty()){
                emPrePlanDeptUserDTOList.forEach(
                        e -> {
                            e.setEmDeptId(dept.getId());
                        }
                );
                iEmPrePlanDeptUserService.insertBatchEmPrePlanDeptUser(emPrePlanDeptUserDTOList);
            }
        });
        for (EmPrePlanDept emPrePlanDept : flatListFinal) {
            emPrePlanDeptMapper.insertEmPrePlanDept(emPrePlanDept);
        }
    }



    // 树形结构扁平化
    public static List<EmPrePlanDeptDTO> flattenTree(List<EmPrePlanDeptDTO> tree) {
        List<EmPrePlanDeptDTO> result = new ArrayList<>();
        flattenNodes(tree, result);
        return result;
    }

    private static void flattenNodes(List<EmPrePlanDeptDTO> nodes,
                                     List<EmPrePlanDeptDTO> result) {
        for (EmPrePlanDeptDTO node : nodes) {
            result.add(node);
            if (node.getChildren() != null) {
                flattenNodes(node.getChildren(), result);
            }
        }
    }
    public static void generateIds(List<EmPrePlanDeptDTO> treeList,String emPrePlanID) {
        if (treeList == null) return;

        for (EmPrePlanDeptDTO node : treeList) {
            // 生成节点ID
            node.setId(UUID.randomUUID().toString());
            node.setPrePlanId(emPrePlanID);
            // 处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                for (EmPrePlanDeptDTO child : node.getChildren()) {
                    // 设置子节点的父ID
                    child.setParentId(node.getId());
                    // 设置子节点级别
                    child.setDeptLevel(1);
                }
                // 递归处理子节点
                generateIds(node.getChildren(),emPrePlanID);
            }
        }
    }
}
