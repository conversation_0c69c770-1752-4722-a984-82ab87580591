package com.tocc.mapper;

import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.vo.WarehouseVO;

import java.util.List;

/**
 * 仓库Mapper接口
 * 
 * <AUTHOR>
 */
public interface WarehouseMapper {
    
    /**
     * 查询仓库
     * 
     * @param id 仓库主键
     * @return 仓库
     */
    WarehouseVO selectWarehouseById(String id);

    /**
     * 查询仓库列表
     * 
     * @param warehouse 仓库
     * @return 仓库集合
     */
    List<WarehouseVO> selectWarehouseList(WarehouseDTO warehouse);

    /**
     * 新增仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    int insertWarehouse(WarehouseDTO warehouse);

    /**
     * 修改仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    int updateWarehouse(WarehouseDTO warehouse);

    /**
     * 删除仓库
     * 
     * @param id 仓库主键
     * @return 结果
     */
    int deleteWarehouseById(String id);

    /**
     * 批量删除仓库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteWarehouseByIds(String[] ids);
}
