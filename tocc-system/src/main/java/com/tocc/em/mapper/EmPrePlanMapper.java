package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.qo.EmPrePlanQO;

/**
 * 应急预案数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmPrePlanMapper 
{
    /**
     * 查询应急预案数据
     * 
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    public EmPrePlan selectEmPrePlanById(String id);

    /**
     * 查询应急预案数据列表
     * 
     * @param emPrePlan 应急预案数据
     * @return 应急预案数据集合
     */
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlan);

    /**
     * 新增应急预案数据
     * 
     * @param emPrePlan 应急预案数据
     * @return 结果
     */
    public int insertEmPrePlan(EmPrePlan emPrePlan);

    /**
     * 修改应急预案数据
     * 
     * @param emPrePlan 应急预案数据
     * @return 结果
     */
    public int updateEmPrePlan(EmPrePlan emPrePlan);

    /**
     * 删除应急预案数据
     * 
     * @param id 应急预案数据主键
     * @return 结果
     */
    public int deleteEmPrePlanById(String id);

    /**
     * 批量删除应急预案数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanByIds(String[] ids);
}
