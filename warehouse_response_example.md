# 仓库接口响应示例（物资装备分离版）

## 仓库详情接口响应示例

### 请求
```
GET /warehouse/8c18a5f6729d4f89a8ce7f22816e4f16
```

### 响应（修改后 - 物资装备分离）
```json
{
  "msg": "操作成功",                // 响应消息
  "code": 200,                     // 响应状态码：200-成功
  "data": {                        // 仓库详情数据
    // ===== 仓库基础信息 =====
    "id": "8c18a5f6729d4f89a8ce7f22816e4f16",        // 仓库唯一标识（UUID格式）
    "warehouseName": "那马应急仓库",                   // 仓库名称
    "warehouseType": "1",                            // 仓库类型值
    "warehouseTypeName": null,                       // 仓库类型名称（字典转换，可能为null）
    "belongOrgCode": "109",                          // 所属单位编码
    "belongOrgName": "南宁市交通运输局",              // 所属单位名称
    "address": "G75兰海高速那马管理区",               // 详细地址
    
    // ===== 负责人信息 =====
    "principal": "张仓管",                           // 负责人姓名
    "contactPhone": "13800138000",                   // 联系电话
    
    // ===== 地理位置信息 =====
    "roadCode": "G72",                               // 路段编号
    "stake": "K100+500",                            // 桩号
    "latitude": "22.619315",                         // 纬度（字符串格式）
    "longitude": "108.384048",                       // 经度（字符串格式）
    
    // ===== 系统字段 =====
    "remark": "那马应急仓库",                        // 备注信息
    "createTime": "2025-06-03 10:53:26",           // 创建时间（yyyy-MM-dd HH:mm:ss格式）
    "creator": "admin",                              // 创建人
    "updateTime": "2025-06-03 10:53:26",           // 最后更新时间
    "updater": null,                                 // 最后更新人（可能为null）
    
    // ===== 统计信息 =====
    "totalMaterialCount": 4,                         // 仓库内物资总数量
    
    // ===== 关联的物资列表（应急物资 materialType="0"）=====
    "materials": [
      {
        "id": "7a0edf7490e04dbca0d76d21c40a9952",    // 物资唯一标识（UUID格式）
        "materialName": "应急帐篷",                  // 物资名称
        "materialType": "0",                         // 物资类型：0-应急物资
        "materialTypeName": "应急物资",              // 物资类型名称
        "specModel": "3x3米防水帐篷",               // 规格型号
        "categoryCode": "SHELTER",                   // 物资类别编码
        "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16", // 所属仓库ID
        "quantity": 200,                             // 库存数量
        "unit": "顶",                                // 计量单位
        "status": 1,                                 // 物资状态：1-正常
        "statusName": "正常",                        // 物资状态名称
        "expiryDate": "2025-12-31",                 // 有效期（yyyy-MM-dd格式）
        "remark": "防水防风应急帐篷，适用于灾民临时安置", // 备注信息
        "teamId": null,                              // 所属队伍ID（仓库物资为null）
        "createTime": "2025-06-03 10:53:26",       // 创建时间
        "creator": "admin",                          // 创建人
        "updateTime": "2025-06-03 10:53:26",       // 更新时间
        "updater": null                              // 更新人（可能为null）
      },
      {
        "id": "a57f48d309d04a898994ba7206b1e29b",
        "materialName": "应急食品",
        "materialType": "0",                         // 物资类型：0-应急物资
        "materialTypeName": "应急物资",
        "specModel": "压缩饼干500g装",
        "categoryCode": "FOOD",                      // 食品类别
        "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
        "quantity": 1000,                            // 大批量库存
        "unit": "包",
        "status": 1,
        "statusName": "正常",
        "expiryDate": "2025-06-30",                 // 食品有有效期
        "remark": "高能量压缩食品，保质期2年",
        "teamId": null,
        "createTime": "2025-06-03 10:53:26",
        "creator": "admin",
        "updateTime": "2025-06-03 10:53:26",
        "updater": null
      }
    ],
    
    // ===== 关联的装备列表（应急装备 materialType="1"）=====
    "equipments": [
      {
        "id": "1a3ddc8f7d1e49e18563bf15ade66082",
        "materialName": "发电机",
        "materialType": "1",                         // 物资类型：1-应急装备
        "materialTypeName": "应急装备",
        "specModel": "5KW汽油发电机",
        "categoryCode": "POWER",                     // 电力设备类别
        "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
        "quantity": 15,
        "unit": "台",
        "status": 1,
        "statusName": "正常",
        "expiryDate": null,                          // 装备通常无有效期
        "remark": "应急供电设备，可连续工作8小时",
        "teamId": null,
        "createTime": "2025-06-03 10:53:26",
        "creator": "admin",
        "updateTime": "2025-06-03 10:53:26",
        "updater": null
      },
      {
        "id": "fdb058a188874054b45c0b1fa92a1d1c",
        "materialName": "救援照明灯",
        "materialType": "1",                         // 物资类型：1-应急装备
        "materialTypeName": "应急装备",
        "specModel": "LED强光照明灯",
        "categoryCode": "LIGHTING",                  // 照明设备类别
        "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
        "quantity": 50,
        "unit": "个",
        "status": 1,
        "statusName": "正常",
        "expiryDate": null,                          // 装备无有效期
        "remark": "便携式强光照明设备，续航12小时",
        "teamId": null,
        "createTime": "2025-06-03 10:53:26",
        "creator": "admin",
        "updateTime": "2025-06-03 10:53:26",
        "updater": null
      }
    ]
  }
}
```

## 仓库列表接口响应示例

### 请求
```
GET /warehouse/list?pageNum=1&pageSize=10
```

### 响应（基于实际返回数据 - 物资装备分离）
```json
{
  "total": 2,                    // 总记录数，用于分页计算
  "rows": [                      // 当前页数据列表
    {
      // ===== 仓库基础信息 =====
      "id": "8c18a5f6729d4f89a8ce7f22816e4f16",        // 仓库唯一标识（UUID格式）
      "warehouseName": "那马应急仓库",                   // 仓库名称
      "warehouseType": "1",                            // 仓库类型值
      "warehouseTypeName": null,                       // 仓库类型名称（字典转换，可能为null）
      "belongOrgCode": "109",                          // 所属单位编码
      "belongOrgName": "南宁市交通运输局",              // 所属单位名称
      "address": "G75兰海高速那马管理区",               // 详细地址

      // ===== 负责人信息 =====
      "principal": "张仓管",                           // 负责人姓名
      "contactPhone": "13800138000",                   // 联系电话

      // ===== 地理位置信息 =====
      "roadCode": "G72",                               // 路段编号
      "stake": "K100+500",                            // 桩号
      "latitude": "22.619315",                         // 纬度（字符串格式）
      "longitude": "108.384048",                       // 经度（字符串格式）

      // ===== 系统字段 =====
      "remark": "那马应急仓库",                        // 备注信息
      "createTime": "2025-06-03 10:53:26",           // 创建时间（yyyy-MM-dd HH:mm:ss格式）
      "creator": "admin",                              // 创建人
      "updateTime": "2025-06-03 10:53:26",           // 最后更新时间
      "updater": null,                                 // 最后更新人（可能为null）

      // ===== 关联的物资列表（应急物资 materialType="0"）=====
      "materials": [
        {
          "id": "7a0edf7490e04dbca0d76d21c40a9952",    // 物资唯一标识（UUID格式）
          "materialName": "应急帐篷",                  // 物资名称
          "materialType": "0",                         // 物资类型：0-应急物资
          "materialTypeName": "应急物资",              // 物资类型名称
          "specModel": "3x3米防水帐篷",               // 规格型号
          "categoryCode": "SHELTER",                   // 物资类别编码
          "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16", // 所属仓库ID
          "quantity": 200,                             // 库存数量
          "unit": "顶",                                // 计量单位
          "status": 1,                                 // 物资状态：1-正常
          "statusName": "正常",                        // 物资状态名称
          "expiryDate": "2025-12-31",                 // 有效期（yyyy-MM-dd格式）
          "remark": "防水防风应急帐篷，适用于灾民临时安置", // 备注信息
          "teamId": null,                              // 所属队伍ID（仓库物资为null）
          "createTime": "2025-06-03 10:53:26",       // 创建时间
          "creator": "admin",                          // 创建人
          "updateTime": "2025-06-03 10:53:26",       // 更新时间
          "updater": null                              // 更新人（可能为null）
        },
        {
          "id": "a57f48d309d04a898994ba7206b1e29b",
          "materialName": "应急食品",
          "materialType": "0",                         // 物资类型：0-应急物资
          "materialTypeName": "应急物资",
          "specModel": "压缩饼干500g装",
          "categoryCode": "FOOD",                      // 食品类别
          "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
          "quantity": 1000,                            // 大批量库存
          "unit": "包",
          "status": 1,
          "statusName": "正常",
          "expiryDate": "2025-06-30",                 // 食品有有效期
          "remark": "高能量压缩食品，保质期2年",
          "teamId": null,
          "createTime": "2025-06-03 10:53:26",
          "creator": "admin",
          "updateTime": "2025-06-03 10:53:26",
          "updater": null
        }
      ],

      // ===== 关联的装备列表（应急装备 materialType="1"）=====
      "equipments": [
        {
          "id": "1a3ddc8f7d1e49e18563bf15ade66082",
          "materialName": "发电机",
          "materialType": "1",                         // 物资类型：1-应急装备
          "materialTypeName": "应急装备",
          "specModel": "5KW汽油发电机",
          "categoryCode": "POWER",                     // 电力设备类别
          "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
          "quantity": 15,
          "unit": "台",
          "status": 1,
          "statusName": "正常",
          "expiryDate": null,                          // 装备通常无有效期
          "remark": "应急供电设备，可连续工作8小时",
          "teamId": null,
          "createTime": "2025-06-03 10:53:26",
          "creator": "admin",
          "updateTime": "2025-06-03 10:53:26",
          "updater": null
        },
        {
          "id": "fdb058a188874054b45c0b1fa92a1d1c",
          "materialName": "救援照明灯",
          "materialType": "1",                         // 物资类型：1-应急装备
          "materialTypeName": "应急装备",
          "specModel": "LED强光照明灯",
          "categoryCode": "LIGHTING",                  // 照明设备类别
          "warehouseId": "8c18a5f6729d4f89a8ce7f22816e4f16",
          "quantity": 50,
          "unit": "个",
          "status": 1,
          "statusName": "正常",
          "expiryDate": null,                          // 装备无有效期
          "remark": "便携式强光照明设备，续航12小时",
          "teamId": null,
          "createTime": "2025-06-03 10:53:26",
          "creator": "admin",
          "updateTime": "2025-06-03 10:53:26",
          "updater": null
        }
      ],

      // ===== 统计信息 =====
      "totalMaterialCount": 4                         // 仓库内物资总数量（物资+装备）
    }
    // 其他仓库数据...
  ],
  "code": 200,                   // 响应状态码：200-成功
  "msg": "查询成功"              // 响应消息
}
```

## 修改说明

### 主要变化
1. **新增equipments字段**：专门存放应急装备（materialType="1"）
2. **materials字段调整**：只存放应急物资（materialType="0"）
3. **数据分离**：前端可以分别处理物资和装备

### 与救援队伍保持一致
- 数据结构与救援队伍接口保持一致
- 都有materials和equipments两个数组
- 便于前端统一处理逻辑

### 前端使用建议
```javascript
// 处理仓库数据
const warehouse = response.data;

// 分别处理物资和装备
const materials = warehouse.materials || [];    // 应急物资
const equipments = warehouse.equipments || [];  // 应急装备

// 统计数量
const materialCount = materials.length;
const equipmentCount = equipments.length;
const totalCount = materialCount + equipmentCount;
```
